/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
view.data-v-31bd7746, text.data-v-31bd7746, button.data-v-31bd7746, input.data-v-31bd7746, textarea.data-v-31bd7746, scroll-view.data-v-31bd7746 {
  box-sizing: border-box;
}
page.data-v-31bd7746 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-31bd7746 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-31bd7746 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-31bd7746 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-31bd7746 {
  display: flex;
}
.flex-column.data-v-31bd7746 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-31bd7746 {
  flex: 1;
}
.flex-wrap.data-v-31bd7746 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-31bd7746 {
  color: #262626;
}
.text-secondary.data-v-31bd7746 {
  color: #595959;
}
.text-disabled.data-v-31bd7746 {
  color: #BFBFBF;
}
.text-success.data-v-31bd7746 {
  color: #52C41A;
}
.text-warning.data-v-31bd7746 {
  color: #FAAD14;
}
.text-error.data-v-31bd7746 {
  color: #F5222D;
}
.text-primary-color.data-v-31bd7746 {
  color: #2E8B57;
}
.text-center.data-v-31bd7746 {
  text-align: center;
}
.text-left.data-v-31bd7746 {
  text-align: left;
}
.text-right.data-v-31bd7746 {
  text-align: right;
}
.text-bold.data-v-31bd7746 {
  font-weight: bold;
}
.text-normal.data-v-31bd7746 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-31bd7746 {
  font-size: 20rpx;
}
.text-sm.data-v-31bd7746 {
  font-size: 24rpx;
}
.text-base.data-v-31bd7746 {
  font-size: 28rpx;
}
.text-lg.data-v-31bd7746 {
  font-size: 32rpx;
}
.text-xl.data-v-31bd7746 {
  font-size: 36rpx;
}
.text-2xl.data-v-31bd7746 {
  font-size: 40rpx;
}
.text-3xl.data-v-31bd7746 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-31bd7746 {
  margin: 0;
}
.m-1.data-v-31bd7746 {
  margin: 8rpx;
}
.m-2.data-v-31bd7746 {
  margin: 16rpx;
}
.m-3.data-v-31bd7746 {
  margin: 24rpx;
}
.m-4.data-v-31bd7746 {
  margin: 32rpx;
}
.m-5.data-v-31bd7746 {
  margin: 40rpx;
}
.mt-0.data-v-31bd7746 {
  margin-top: 0;
}
.mt-1.data-v-31bd7746 {
  margin-top: 8rpx;
}
.mt-2.data-v-31bd7746 {
  margin-top: 16rpx;
}
.mt-3.data-v-31bd7746 {
  margin-top: 24rpx;
}
.mt-4.data-v-31bd7746 {
  margin-top: 32rpx;
}
.mt-5.data-v-31bd7746 {
  margin-top: 40rpx;
}
.mb-0.data-v-31bd7746 {
  margin-bottom: 0;
}
.mb-1.data-v-31bd7746 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-31bd7746 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-31bd7746 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-31bd7746 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-31bd7746 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-31bd7746 {
  margin-left: 0;
}
.ml-1.data-v-31bd7746 {
  margin-left: 8rpx;
}
.ml-2.data-v-31bd7746 {
  margin-left: 16rpx;
}
.ml-3.data-v-31bd7746 {
  margin-left: 24rpx;
}
.ml-4.data-v-31bd7746 {
  margin-left: 32rpx;
}
.ml-5.data-v-31bd7746 {
  margin-left: 40rpx;
}
.mr-0.data-v-31bd7746 {
  margin-right: 0;
}
.mr-1.data-v-31bd7746 {
  margin-right: 8rpx;
}
.mr-2.data-v-31bd7746 {
  margin-right: 16rpx;
}
.mr-3.data-v-31bd7746 {
  margin-right: 24rpx;
}
.mr-4.data-v-31bd7746 {
  margin-right: 32rpx;
}
.mr-5.data-v-31bd7746 {
  margin-right: 40rpx;
}
.p-0.data-v-31bd7746 {
  padding: 0;
}
.p-1.data-v-31bd7746 {
  padding: 8rpx;
}
.p-2.data-v-31bd7746 {
  padding: 16rpx;
}
.p-3.data-v-31bd7746 {
  padding: 24rpx;
}
.p-4.data-v-31bd7746 {
  padding: 32rpx;
}
.p-5.data-v-31bd7746 {
  padding: 40rpx;
}
.pt-0.data-v-31bd7746 {
  padding-top: 0;
}
.pt-1.data-v-31bd7746 {
  padding-top: 8rpx;
}
.pt-2.data-v-31bd7746 {
  padding-top: 16rpx;
}
.pt-3.data-v-31bd7746 {
  padding-top: 24rpx;
}
.pt-4.data-v-31bd7746 {
  padding-top: 32rpx;
}
.pt-5.data-v-31bd7746 {
  padding-top: 40rpx;
}
.pb-0.data-v-31bd7746 {
  padding-bottom: 0;
}
.pb-1.data-v-31bd7746 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-31bd7746 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-31bd7746 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-31bd7746 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-31bd7746 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-31bd7746 {
  padding-left: 0;
}
.pl-1.data-v-31bd7746 {
  padding-left: 8rpx;
}
.pl-2.data-v-31bd7746 {
  padding-left: 16rpx;
}
.pl-3.data-v-31bd7746 {
  padding-left: 24rpx;
}
.pl-4.data-v-31bd7746 {
  padding-left: 32rpx;
}
.pl-5.data-v-31bd7746 {
  padding-left: 40rpx;
}
.pr-0.data-v-31bd7746 {
  padding-right: 0;
}
.pr-1.data-v-31bd7746 {
  padding-right: 8rpx;
}
.pr-2.data-v-31bd7746 {
  padding-right: 16rpx;
}
.pr-3.data-v-31bd7746 {
  padding-right: 24rpx;
}
.pr-4.data-v-31bd7746 {
  padding-right: 32rpx;
}
.pr-5.data-v-31bd7746 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-31bd7746 {
  width: 100%;
}
.h-full.data-v-31bd7746 {
  height: 100%;
}
.w-screen.data-v-31bd7746 {
  width: 100vw;
}
.h-screen.data-v-31bd7746 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-31bd7746 {
  border-radius: 0;
}
.rounded-sm.data-v-31bd7746 {
  border-radius: 4rpx;
}
.rounded.data-v-31bd7746 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-31bd7746 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-31bd7746 {
  border-radius: 24rpx;
}
.rounded-full.data-v-31bd7746 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-31bd7746 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-31bd7746 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-31bd7746 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-31bd7746 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-31bd7746 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-31bd7746 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-31bd7746 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-31bd7746 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-31bd7746 {
  background-color: #2E8B57;
}
.bg-light.data-v-31bd7746 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-31bd7746 {
  background-color: #F8F9FA;
}
.bg-success.data-v-31bd7746 {
  background-color: #52C41A;
}
.bg-warning.data-v-31bd7746 {
  background-color: #FAAD14;
}
.bg-error.data-v-31bd7746 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-31bd7746 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-31bd7746 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-31bd7746 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-31bd7746 {
  color: #FAAD14;
}
.status-approved.data-v-31bd7746 {
  color: #52C41A;
}
.status-rejected.data-v-31bd7746 {
  color: #F5222D;
}
.status-not-submitted.data-v-31bd7746 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-31bd7746 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-31bd7746 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-31bd7746 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-31bd7746 {
  animation: fadeIn-31bd7746 0.3s ease-in-out;
}
@keyframes fadeIn-31bd7746 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-31bd7746 {
  animation: slideUp-31bd7746 0.3s ease-out;
}
@keyframes slideUp-31bd7746 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-31bd7746::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-31bd7746 {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-31bd7746,
.fade-leave-active.data-v-31bd7746 {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-31bd7746,
.fade-leave-to.data-v-31bd7746 {
  opacity: 0;
}
.slide-up-enter-active.data-v-31bd7746,
.slide-up-leave-active.data-v-31bd7746 {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-31bd7746,
.slide-up-leave-to.data-v-31bd7746 {
  transform: translateY(100%);
}
.exam-history-container.data-v-31bd7746 {
  min-height: 100vh;
  background: #F8F9FA;
}
.stats-section.data-v-31bd7746 {
  margin: 24rpx;
}
.stats-section .stats-card.data-v-31bd7746 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}
.stats-section .stats-card .stat-item.data-v-31bd7746 {
  flex: 1;
  text-align: center;
}
.stats-section .stats-card .stat-item .stat-number.data-v-31bd7746 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}
.stats-section .stats-card .stat-item .stat-label.data-v-31bd7746 {
  font-size: 24rpx;
  color: #666;
}
.stats-section .stats-card .stat-divider.data-v-31bd7746 {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}
.filter-section.data-v-31bd7746 {
  margin: 0 24rpx 24rpx;
}
.filter-section .filter-tabs.data-v-31bd7746 {
  background: #fff;
  border-radius: 16rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.filter-section .filter-tabs .filter-tab.data-v-31bd7746 {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.filter-section .filter-tabs .filter-tab.active.data-v-31bd7746 {
  background: #4A90E2;
}
.filter-section .filter-tabs .filter-tab.active text.data-v-31bd7746 {
  color: #fff;
  font-weight: bold;
}
.filter-section .filter-tabs .filter-tab text.data-v-31bd7746 {
  font-size: 26rpx;
  color: #666;
}
.exam-list.data-v-31bd7746 {
  padding: 0 24rpx;
}
.exam-list .exam-item.data-v-31bd7746 {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.exam-list .exam-item .exam-header.data-v-31bd7746 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.exam-list .exam-item .exam-header .exam-info.data-v-31bd7746 {
  flex: 1;
}
.exam-list .exam-item .exam-header .exam-info .exam-name.data-v-31bd7746 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.exam-list .exam-item .exam-header .exam-info .exam-time.data-v-31bd7746 {
  font-size: 24rpx;
  color: #999;
}
.exam-list .exam-item .exam-content.data-v-31bd7746 {
  margin-bottom: 24rpx;
}
.exam-list .exam-item .exam-content .score-section.data-v-31bd7746 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.exam-list .exam-item .exam-content .score-section .score-display.data-v-31bd7746 {
  display: flex;
  align-items: baseline;
  margin-right: 32rpx;
}
.exam-list .exam-item .exam-content .score-section .score-display .score-number.data-v-31bd7746 {
  font-size: 48rpx;
  font-weight: bold;
}
.exam-list .exam-item .exam-content .score-section .score-display .score-unit.data-v-31bd7746 {
  font-size: 24rpx;
  margin-left: 4rpx;
}
.exam-list .exam-item .exam-content .score-section .score-display.excellent.data-v-31bd7746 {
  color: #4CAF50;
}
.exam-list .exam-item .exam-content .score-section .score-display.good.data-v-31bd7746 {
  color: #4A90E2;
}
.exam-list .exam-item .exam-content .score-section .score-display.normal.data-v-31bd7746 {
  color: #FF9500;
}
.exam-list .exam-item .exam-content .score-section .score-display.poor.data-v-31bd7746 {
  color: #f56c6c;
}
.exam-list .exam-item .exam-content .score-section .score-info.data-v-31bd7746 {
  flex: 1;
}
.exam-list .exam-item .exam-content .score-section .score-info .pass-status.data-v-31bd7746 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #f56c6c;
  margin-bottom: 8rpx;
}
.exam-list .exam-item .exam-content .score-section .score-info .pass-status.passed.data-v-31bd7746 {
  color: #4CAF50;
}
.exam-list .exam-item .exam-content .score-section .score-info .score-detail.data-v-31bd7746 {
  font-size: 24rpx;
  color: #666;
}
.exam-list .exam-item .exam-content .exam-meta.data-v-31bd7746 {
  display: flex;
  gap: 32rpx;
}
.exam-list .exam-item .exam-content .exam-meta .meta-item.data-v-31bd7746 {
  display: flex;
  align-items: center;
}
.exam-list .exam-item .exam-content .exam-meta .meta-item text.data-v-31bd7746 {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #666;
}
.exam-list .exam-item .exam-actions.data-v-31bd7746 {
  display: flex;
  gap: 16rpx;
}
.exam-list .exam-item .exam-actions .detail-btn.data-v-31bd7746,
.exam-list .exam-item .exam-actions .certificate-btn.data-v-31bd7746 {
  flex: 1;
  height: 64rpx;
  border-radius: 32rpx;
}
.load-more.data-v-31bd7746 {
  text-align: center;
  padding: 40rpx;
}
.load-more text.data-v-31bd7746 {
  font-size: 28rpx;
  color: #4A90E2;
}