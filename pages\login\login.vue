<template>
  <view class="login-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- Logo和标题 -->
      <view class="header">
        <view class="logo-placeholder">
          <text class="logo-text">疾控</text>
        </view>
        <text class="app-name">疾控医护考试系统</text>
        <text class="app-desc">医护人员任职资格考试平台</text>
      </view>

      <!-- 登录表单 -->
      <view class="login-form">
        <!-- 协议勾选 -->
        <view class="agreement-section">
          <checkbox
            :checked="agreedToTerms"
            @change="onAgreementChange"
            color="#2E8B57"
          />
          <text class="agreement-text">
            我已阅读并同意《用户服务协议》和《隐私政策》
          </text>
        </view>

        <!-- 登录按钮 -->
        <button
          class="login-btn"
          :disabled="!agreedToTerms || isLogging"
          @click="handleLogin"
        >
          {{ isLogging ? '登录中...' : '开始使用' }}
        </button>

        <!-- 温馨提示 -->
        <view class="tips">
          <text class="tips-text">首次登录需完善个人资料，通过机构审核后可使用完整功能</text>
        </view>
      </view>
    </view>

    <!-- 底部版权信息 -->
    <view class="footer">
      <text class="copyright">© 2024 疾控医护考试系统</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const agreedToTerms = ref(false)
const isLogging = ref(false)

// 协议勾选处理
const onAgreementChange = (e: any) => {
  agreedToTerms.value = e.detail.value.length > 0
}

// 登录处理
const handleLogin = async () => {
  if (!agreedToTerms.value) {
    uni.showToast({
      title: '请先同意用户协议',
      icon: 'none'
    })
    return
  }

  isLogging.value = true

  try {
    // 模拟登录过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 跳转到信息中心页面
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/info/info'
      })
    }, 1500)

  } catch (error: any) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    })
  } finally {
    isLogging.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 60rpx;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 500rpx;
}

.header {
  text-align: center;
  margin-bottom: 100rpx;

  .logo-placeholder {
    width: 120rpx;
    height: 120rpx;
    border-radius: 24rpx;
    margin: 0 auto 40rpx;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

    .logo-text {
      font-size: 32rpx;
      font-weight: bold;
      color: #fff;
    }
  }

  .app-name {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: #fff;
    margin-bottom: 16rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }

  .app-desc {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
  }
}

.login-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}

.agreement-section {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;

  checkbox {
    margin-right: 12rpx;
    margin-top: 4rpx;
  }

  .agreement-text {
    flex: 1;
    font-size: 26rpx;
    color: #666;
    line-height: 1.5;
  }
}

.login-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);
  margin-bottom: 40rpx;

  &:disabled {
    background: #ccc;
    box-shadow: none;
  }
}

.tips {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #2E8B57;

  .tips-text {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.footer {
  margin-top: 60rpx;

  .copyright {
    font-size: 24rpx;
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
  }
}
</style>
