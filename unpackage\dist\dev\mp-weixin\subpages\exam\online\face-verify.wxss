/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
view.data-v-d787804b, text.data-v-d787804b, button.data-v-d787804b, input.data-v-d787804b, textarea.data-v-d787804b, scroll-view.data-v-d787804b {
  box-sizing: border-box;
}
page.data-v-d787804b {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-d787804b {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-d787804b {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-d787804b {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-d787804b {
  display: flex;
}
.flex-column.data-v-d787804b {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-d787804b {
  flex: 1;
}
.flex-wrap.data-v-d787804b {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-d787804b {
  color: #262626;
}
.text-secondary.data-v-d787804b {
  color: #595959;
}
.text-disabled.data-v-d787804b {
  color: #BFBFBF;
}
.text-success.data-v-d787804b {
  color: #52C41A;
}
.text-warning.data-v-d787804b {
  color: #FAAD14;
}
.text-error.data-v-d787804b {
  color: #F5222D;
}
.text-primary-color.data-v-d787804b {
  color: #2E8B57;
}
.text-center.data-v-d787804b {
  text-align: center;
}
.text-left.data-v-d787804b {
  text-align: left;
}
.text-right.data-v-d787804b {
  text-align: right;
}
.text-bold.data-v-d787804b {
  font-weight: bold;
}
.text-normal.data-v-d787804b {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-d787804b {
  font-size: 20rpx;
}
.text-sm.data-v-d787804b {
  font-size: 24rpx;
}
.text-base.data-v-d787804b {
  font-size: 28rpx;
}
.text-lg.data-v-d787804b {
  font-size: 32rpx;
}
.text-xl.data-v-d787804b {
  font-size: 36rpx;
}
.text-2xl.data-v-d787804b {
  font-size: 40rpx;
}
.text-3xl.data-v-d787804b {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-d787804b {
  margin: 0;
}
.m-1.data-v-d787804b {
  margin: 8rpx;
}
.m-2.data-v-d787804b {
  margin: 16rpx;
}
.m-3.data-v-d787804b {
  margin: 24rpx;
}
.m-4.data-v-d787804b {
  margin: 32rpx;
}
.m-5.data-v-d787804b {
  margin: 40rpx;
}
.mt-0.data-v-d787804b {
  margin-top: 0;
}
.mt-1.data-v-d787804b {
  margin-top: 8rpx;
}
.mt-2.data-v-d787804b {
  margin-top: 16rpx;
}
.mt-3.data-v-d787804b {
  margin-top: 24rpx;
}
.mt-4.data-v-d787804b {
  margin-top: 32rpx;
}
.mt-5.data-v-d787804b {
  margin-top: 40rpx;
}
.mb-0.data-v-d787804b {
  margin-bottom: 0;
}
.mb-1.data-v-d787804b {
  margin-bottom: 8rpx;
}
.mb-2.data-v-d787804b {
  margin-bottom: 16rpx;
}
.mb-3.data-v-d787804b {
  margin-bottom: 24rpx;
}
.mb-4.data-v-d787804b {
  margin-bottom: 32rpx;
}
.mb-5.data-v-d787804b {
  margin-bottom: 40rpx;
}
.ml-0.data-v-d787804b {
  margin-left: 0;
}
.ml-1.data-v-d787804b {
  margin-left: 8rpx;
}
.ml-2.data-v-d787804b {
  margin-left: 16rpx;
}
.ml-3.data-v-d787804b {
  margin-left: 24rpx;
}
.ml-4.data-v-d787804b {
  margin-left: 32rpx;
}
.ml-5.data-v-d787804b {
  margin-left: 40rpx;
}
.mr-0.data-v-d787804b {
  margin-right: 0;
}
.mr-1.data-v-d787804b {
  margin-right: 8rpx;
}
.mr-2.data-v-d787804b {
  margin-right: 16rpx;
}
.mr-3.data-v-d787804b {
  margin-right: 24rpx;
}
.mr-4.data-v-d787804b {
  margin-right: 32rpx;
}
.mr-5.data-v-d787804b {
  margin-right: 40rpx;
}
.p-0.data-v-d787804b {
  padding: 0;
}
.p-1.data-v-d787804b {
  padding: 8rpx;
}
.p-2.data-v-d787804b {
  padding: 16rpx;
}
.p-3.data-v-d787804b {
  padding: 24rpx;
}
.p-4.data-v-d787804b {
  padding: 32rpx;
}
.p-5.data-v-d787804b {
  padding: 40rpx;
}
.pt-0.data-v-d787804b {
  padding-top: 0;
}
.pt-1.data-v-d787804b {
  padding-top: 8rpx;
}
.pt-2.data-v-d787804b {
  padding-top: 16rpx;
}
.pt-3.data-v-d787804b {
  padding-top: 24rpx;
}
.pt-4.data-v-d787804b {
  padding-top: 32rpx;
}
.pt-5.data-v-d787804b {
  padding-top: 40rpx;
}
.pb-0.data-v-d787804b {
  padding-bottom: 0;
}
.pb-1.data-v-d787804b {
  padding-bottom: 8rpx;
}
.pb-2.data-v-d787804b {
  padding-bottom: 16rpx;
}
.pb-3.data-v-d787804b {
  padding-bottom: 24rpx;
}
.pb-4.data-v-d787804b {
  padding-bottom: 32rpx;
}
.pb-5.data-v-d787804b {
  padding-bottom: 40rpx;
}
.pl-0.data-v-d787804b {
  padding-left: 0;
}
.pl-1.data-v-d787804b {
  padding-left: 8rpx;
}
.pl-2.data-v-d787804b {
  padding-left: 16rpx;
}
.pl-3.data-v-d787804b {
  padding-left: 24rpx;
}
.pl-4.data-v-d787804b {
  padding-left: 32rpx;
}
.pl-5.data-v-d787804b {
  padding-left: 40rpx;
}
.pr-0.data-v-d787804b {
  padding-right: 0;
}
.pr-1.data-v-d787804b {
  padding-right: 8rpx;
}
.pr-2.data-v-d787804b {
  padding-right: 16rpx;
}
.pr-3.data-v-d787804b {
  padding-right: 24rpx;
}
.pr-4.data-v-d787804b {
  padding-right: 32rpx;
}
.pr-5.data-v-d787804b {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-d787804b {
  width: 100%;
}
.h-full.data-v-d787804b {
  height: 100%;
}
.w-screen.data-v-d787804b {
  width: 100vw;
}
.h-screen.data-v-d787804b {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-d787804b {
  border-radius: 0;
}
.rounded-sm.data-v-d787804b {
  border-radius: 4rpx;
}
.rounded.data-v-d787804b {
  border-radius: 8rpx;
}
.rounded-lg.data-v-d787804b {
  border-radius: 16rpx;
}
.rounded-xl.data-v-d787804b {
  border-radius: 24rpx;
}
.rounded-full.data-v-d787804b {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-d787804b {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-d787804b {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-d787804b {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-d787804b {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-d787804b {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-d787804b {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-d787804b {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-d787804b {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-d787804b {
  background-color: #2E8B57;
}
.bg-light.data-v-d787804b {
  background-color: #FFFFFF;
}
.bg-gray.data-v-d787804b {
  background-color: #F8F9FA;
}
.bg-success.data-v-d787804b {
  background-color: #52C41A;
}
.bg-warning.data-v-d787804b {
  background-color: #FAAD14;
}
.bg-error.data-v-d787804b {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-d787804b {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-d787804b {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-d787804b {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-d787804b {
  color: #FAAD14;
}
.status-approved.data-v-d787804b {
  color: #52C41A;
}
.status-rejected.data-v-d787804b {
  color: #F5222D;
}
.status-not-submitted.data-v-d787804b {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-d787804b {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-d787804b {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-d787804b {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-d787804b {
  animation: fadeIn-d787804b 0.3s ease-in-out;
}
@keyframes fadeIn-d787804b {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-d787804b {
  animation: slideUp-d787804b 0.3s ease-out;
}
@keyframes slideUp-d787804b {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-d787804b::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-d787804b {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-d787804b,
.fade-leave-active.data-v-d787804b {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-d787804b,
.fade-leave-to.data-v-d787804b {
  opacity: 0;
}
.slide-up-enter-active.data-v-d787804b,
.slide-up-leave-active.data-v-d787804b {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-d787804b,
.slide-up-leave-to.data-v-d787804b {
  transform: translateY(100%);
}
.face-verify-container.data-v-d787804b {
  min-height: 100vh;
  background: #F8F9FA;
}
.verify-steps.data-v-d787804b {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 60rpx;
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.verify-steps .step-item.data-v-d787804b {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.verify-steps .step-item .step-icon.data-v-d787804b {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background: #e9ecef;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}
.verify-steps .step-item .step-text.data-v-d787804b {
  font-size: 24rpx;
  color: #6c757d;
  transition: all 0.3s ease;
}
.verify-steps .step-item.active .step-icon.data-v-d787804b {
  background: #4A90E2;
  color: #fff;
}
.verify-steps .step-item.active .step-text.data-v-d787804b {
  color: #4A90E2;
  font-weight: bold;
}
.verify-steps .step-item.completed .step-icon.data-v-d787804b {
  background: #4CAF50;
  color: #fff;
}
.verify-steps .step-item.completed .step-text.data-v-d787804b {
  color: #4CAF50;
  font-weight: bold;
}
.verify-steps .step-line.data-v-d787804b {
  width: 80rpx;
  height: 4rpx;
  background: #e9ecef;
  margin: 0 20rpx;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}
.verify-steps .step-line.active.data-v-d787804b {
  background: #4CAF50;
}
.step-content.data-v-d787804b {
  margin: 24rpx;
}
.identity-confirm.data-v-d787804b {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.identity-confirm .user-info-card.data-v-d787804b {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}
.identity-confirm .user-info-card .avatar-section.data-v-d787804b {
  margin-right: 32rpx;
}
.identity-confirm .user-info-card .avatar-section .user-avatar.data-v-d787804b {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid #4A90E2;
}
.identity-confirm .user-info-card .info-section.data-v-d787804b {
  flex: 1;
}
.identity-confirm .user-info-card .info-section .user-name.data-v-d787804b {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.identity-confirm .user-info-card .info-section .user-id.data-v-d787804b {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.identity-confirm .user-info-card .info-section .user-org.data-v-d787804b {
  font-size: 24rpx;
  color: #999;
}
.identity-confirm .confirm-tips.data-v-d787804b {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #e6f7ff;
  border-radius: 12rpx;
  border-left: 6rpx solid #4A90E2;
  margin-bottom: 40rpx;
}
.identity-confirm .confirm-tips .tips-text.data-v-d787804b {
  flex: 1;
  margin-left: 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
}
.identity-confirm .confirm-btn.data-v-d787804b {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}
.face-recognition .camera-section.data-v-d787804b {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.face-recognition .camera-section .camera-frame.data-v-d787804b {
  position: relative;
  width: 100%;
  height: 500rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}
.face-recognition .camera-section .camera-frame .camera-view.data-v-d787804b {
  width: 100%;
  height: 100%;
}
.face-recognition .camera-section .camera-frame .camera-placeholder.data-v-d787804b {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.face-recognition .camera-section .camera-frame .camera-placeholder .placeholder-text.data-v-d787804b {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #999;
}
.face-recognition .camera-section .camera-frame .face-frame.data-v-d787804b {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 400rpx;
}
.face-recognition .camera-section .camera-frame .face-frame .frame-corner.data-v-d787804b {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #4A90E2;
}
.face-recognition .camera-section .camera-frame .face-frame .frame-corner.tl.data-v-d787804b {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}
.face-recognition .camera-section .camera-frame .face-frame .frame-corner.tr.data-v-d787804b {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}
.face-recognition .camera-section .camera-frame .face-frame .frame-corner.bl.data-v-d787804b {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}
.face-recognition .camera-section .camera-frame .face-frame .frame-corner.br.data-v-d787804b {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}
.face-recognition .camera-section .camera-frame .verify-status.data-v-d787804b {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}
.face-recognition .camera-section .camera-frame .verify-status.detecting.data-v-d787804b {
  background: rgba(255, 149, 0, 0.9);
  color: #fff;
}
.face-recognition .camera-section .camera-frame .verify-status.verifying.data-v-d787804b {
  background: rgba(74, 144, 226, 0.9);
  color: #fff;
}
.face-recognition .camera-section .camera-frame .verify-status.success.data-v-d787804b {
  background: rgba(76, 175, 80, 0.9);
  color: #fff;
}
.face-recognition .camera-section .camera-frame .verify-status.failed.data-v-d787804b {
  background: rgba(245, 108, 108, 0.9);
  color: #fff;
}
.face-recognition .camera-section .camera-tips .tips-title.data-v-d787804b {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.face-recognition .camera-section .camera-tips .tips-list .tip-item.data-v-d787804b {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.face-recognition .camera-section .camera-tips .tips-list .tip-item text.data-v-d787804b {
  margin-left: 12rpx;
  font-size: 26rpx;
  color: #666;
}
.face-recognition .verify-actions.data-v-d787804b {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.face-recognition .verify-actions .capture-btn.data-v-d787804b {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.face-recognition .verify-actions .retry-tips.data-v-d787804b {
  text-align: center;
}
.face-recognition .verify-actions .retry-tips text.data-v-d787804b {
  font-size: 24rpx;
  color: #f56c6c;
}
.verify-success.data-v-d787804b {
  background: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.verify-success .success-icon.data-v-d787804b {
  margin-bottom: 40rpx;
}
.verify-success .success-title.data-v-d787804b {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}
.verify-success .success-desc.data-v-d787804b {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}
.verify-success .verify-result.data-v-d787804b {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
}
.verify-success .verify-result .result-item.data-v-d787804b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.verify-success .verify-result .result-item.data-v-d787804b:last-child {
  margin-bottom: 0;
}
.verify-success .verify-result .result-item .result-label.data-v-d787804b {
  font-size: 26rpx;
  color: #666;
}
.verify-success .verify-result .result-item .result-value.data-v-d787804b {
  font-size: 26rpx;
  font-weight: bold;
  color: #4A90E2;
}
.verify-success .enter-exam-btn.data-v-d787804b {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}