{"version": 3, "file": "info.js", "sources": ["pages/info/info.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5mby9pbmZvLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"info-center-container\">\n    <!-- 自定义头部 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">信息中心</text>\n      </view>\n    </view>\n\n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 轮播公告 -->\n      <view class=\"banner-section\">\n        <view class=\"banner-item\">\n          <view class=\"banner-content\">\n            <text class=\"banner-title\">欢迎使用疾控医护考试系统</text>\n            <text class=\"banner-desc\">为疾控医护人员提供专业的考试学习平台</text>\n          </view>\n          <view class=\"banner-tag important\">重要</view>\n        </view>\n      </view>\n\n      <!-- 内容分类 -->\n      <view class=\"content-categories\">\n        <view class=\"tab-list\">\n          <view\n            v-for=\"(tab, index) in tabList\"\n            :key=\"tab.key\"\n            class=\"tab-item\"\n            :class=\"{ active: currentTab === index }\"\n            @click=\"onTabChange(index)\"\n          >\n            <text>{{ tab.name }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 内容列表 -->\n      <view class=\"content-list\">\n        <scroll-view\n          class=\"list-scroll\"\n          scroll-y\n          @scrolltolower=\"onLoadMore\"\n        >\n          <!-- 内容列表 -->\n          <view v-if=\"currentList.length > 0\">\n            <view\n              v-for=\"item in currentList\"\n              :key=\"item.id\"\n              class=\"content-item\"\n              @click=\"viewDetail(item)\"\n            >\n              <view class=\"item-header\">\n                <text class=\"item-title\">{{ item.title }}</text>\n                <view v-if=\"item.isImportant\" class=\"important-tag\">\n                  <text>重要</text>\n                </view>\n              </view>\n\n              <text class=\"item-summary\">{{ item.content }}</text>\n\n              <view class=\"item-footer\">\n                <text class=\"item-date\">{{ item.publishTime }}</text>\n                <text class=\"item-source\">{{ item.source }}</text>\n              </view>\n            </view>\n          </view>\n\n          <!-- 空状态 -->\n          <view v-else class=\"empty-state\">\n            <text class=\"empty-text\">暂无内容</text>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed } from 'vue'\n\n// 数据状态\nconst currentTab = ref(0)\n\n// Tab配置\nconst tabList = [\n  { name: '全部', key: 'all' },\n  { name: '公告', key: 'notice' },\n  { name: '政策法规', key: 'policy' },\n  { name: '重要通知', key: 'news' }\n]\n\n// 模拟数据\nconst mockData = [\n  {\n    id: 1,\n    title: '关于开展2024年疾控医护人员任职资格考试的通知',\n    content: '为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。',\n    publishTime: '2024-01-15',\n    source: '疾控中心',\n    type: 'notice',\n    isImportant: true\n  },\n  {\n    id: 2,\n    title: '疾控医护人员继续教育管理办法',\n    content: '为加强疾控医护人员继续教育管理，提高专业技术水平，制定本办法。',\n    publishTime: '2024-01-10',\n    source: '卫健委',\n    type: 'policy',\n    isImportant: false\n  },\n  {\n    id: 3,\n    title: '考试系统使用指南',\n    content: '本指南详细介绍了疾控医护考试系统的使用方法，包括注册、登录、考试等功能。',\n    publishTime: '2024-01-08',\n    source: '技术部',\n    type: 'news',\n    isImportant: false\n  }\n]\n\nconst allList = ref(mockData)\n\n// 计算当前列表\nconst currentList = computed(() => {\n  const currentTabKey = tabList[currentTab.value].key\n  if (currentTabKey === 'all') {\n    return allList.value\n  }\n  return allList.value.filter(item => item.type === currentTabKey)\n})\n\n// 切换Tab\nconst onTabChange = (index: number) => {\n  currentTab.value = index\n}\n\n// 加载更多\nconst onLoadMore = () => {\n  uni.__f__('log','at pages/info/info.vue:142','加载更多')\n}\n\n// 查看详情\nconst viewDetail = (item: any) => {\n  uni.navigateTo({\n    url: `/pages/info/detail?id=${item.id}`\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.info-center-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n}\n\n.header {\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  padding: 20rpx 30rpx 40rpx;\n\n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n\n    .page-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n  }\n}\n\n.main-content {\n  padding-bottom: 120rpx;\n}\n\n.banner-section {\n  margin: 30rpx;\n\n  .banner-item {\n    position: relative;\n    height: 200rpx;\n    background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);\n    padding: 40rpx;\n    display: flex;\n    align-items: center;\n    border-radius: 24rpx;\n\n    .banner-content {\n      flex: 1;\n\n      .banner-title {\n        display: block;\n        font-size: 30rpx;\n        font-weight: bold;\n        color: #fff;\n        margin-bottom: 16rpx;\n        line-height: 1.4;\n      }\n\n      .banner-desc {\n        font-size: 24rpx;\n        color: rgba(255, 255, 255, 0.8);\n        line-height: 1.4;\n      }\n    }\n\n    .banner-tag {\n      position: absolute;\n      top: 20rpx;\n      right: 20rpx;\n      padding: 8rpx 16rpx;\n      background: rgba(245, 34, 45, 0.9);\n      border-radius: 12rpx;\n      font-size: 20rpx;\n      color: #fff;\n    }\n  }\n}\n\n.content-categories {\n  background: #fff;\n  padding: 0 30rpx;\n  border-bottom: 2rpx solid #f0f0f0;\n\n  .tab-list {\n    display: flex;\n\n    .tab-item {\n      flex: 1;\n      text-align: center;\n      padding: 30rpx 0;\n      font-size: 28rpx;\n      color: #666;\n      border-bottom: 4rpx solid transparent;\n\n      &.active {\n        color: #2E8B57;\n        border-bottom-color: #2E8B57;\n        font-weight: bold;\n      }\n    }\n  }\n}\n\n.content-list {\n  .list-scroll {\n    height: calc(100vh - 400rpx);\n  }\n\n  .content-item {\n    margin: 20rpx 30rpx;\n    padding: 40rpx;\n    background: #fff;\n    border-radius: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n\n    .item-header {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      margin-bottom: 20rpx;\n      gap: 16rpx;\n\n      .item-title {\n        flex: 1;\n        font-size: 30rpx;\n        font-weight: bold;\n        color: #262626;\n        line-height: 1.4;\n      }\n\n      .important-tag {\n        padding: 8rpx 16rpx;\n        background: #fff3e0;\n        border-radius: 12rpx;\n        border: 2rpx solid #FAAD14;\n\n        text {\n          font-size: 20rpx;\n          color: #FAAD14;\n          font-weight: bold;\n        }\n      }\n    }\n\n    .item-summary {\n      display: block;\n      font-size: 26rpx;\n      color: #595959;\n      line-height: 1.5;\n      margin-bottom: 24rpx;\n    }\n\n    .item-footer {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .item-date {\n        font-size: 24rpx;\n        color: #BFBFBF;\n      }\n\n      .item-source {\n        font-size: 24rpx;\n        color: #BFBFBF;\n      }\n    }\n  }\n}\n\n.empty-state {\n  text-align: center;\n  padding: 100rpx 40rpx;\n\n  .empty-text {\n    font-size: 28rpx;\n    color: #BFBFBF;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/info/info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni"], "mappings": ";;;;;AAkFM,UAAA,aAAaA,kBAAI,CAAC;AAGxB,UAAM,UAAU;AAAA,MACd,EAAE,MAAM,MAAM,KAAK,MAAM;AAAA,MACzB,EAAE,MAAM,MAAM,KAAK,SAAS;AAAA,MAC5B,EAAE,MAAM,QAAQ,KAAK,SAAS;AAAA,MAC9B,EAAE,MAAM,QAAQ,KAAK,OAAO;AAAA,IAAA;AAI9B,UAAM,WAAW;AAAA,MACf;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,MACA;AAAA,QACE,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IAAA;AAGI,UAAA,UAAUA,kBAAI,QAAQ;AAGtB,UAAA,cAAcC,cAAAA,SAAS,MAAM;AACjC,YAAM,gBAAgB,QAAQ,WAAW,KAAK,EAAE;AAChD,UAAI,kBAAkB,OAAO;AAC3B,eAAO,QAAQ;AAAA,MACjB;AACA,aAAO,QAAQ,MAAM,OAAO,CAAQ,SAAA,KAAK,SAAS,aAAa;AAAA,IAAA,CAChE;AAGK,UAAA,cAAc,CAAC,UAAkB;AACrC,iBAAW,QAAQ;AAAA,IAAA;AAIrB,UAAM,aAAa,MAAM;AACnBC,oBAAAA,MAAA,MAAM,OAAM,8BAA6B,MAAM;AAAA,IAAA;AAI/C,UAAA,aAAa,CAAC,SAAc;AAChCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,KAAK,EAAE;AAAA,MAAA,CACtC;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnJH,GAAG,WAAW,eAAe;"}