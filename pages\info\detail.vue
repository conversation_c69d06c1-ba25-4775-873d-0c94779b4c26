<template>
  <view class="detail-container">
    <!-- 详情内容 -->
    <view class="detail-content">
      <!-- 文章头部 -->
      <view class="article-header">
        <view class="header-tags">
          <view v-if="detail.isImportant" class="important-tag">
            <text>重要</text>
          </view>
        </view>

        <text class="article-title">{{ detail.title }}</text>

        <view class="article-meta">
          <view class="meta-item">
            <text>📅 {{ detail.publishTime }}</text>
          </view>
          <view v-if="detail.source" class="meta-item">
            <text>🏢 {{ detail.source }}</text>
          </view>
        </view>
      </view>

      <!-- 分割线 -->
      <view class="divider"></view>

      <!-- 文章内容 -->
      <view class="article-body">
        <text class="content-text">{{ detail.content }}</text>
      </view>

      <!-- 底部操作 -->
      <view class="article-actions">
        <button class="action-btn" @click="handleShare">分享</button>
        <button class="action-btn" @click="handleCollect">
          {{ isCollected ? '已收藏' : '收藏' }}
        </button>
        <button class="action-btn" @click="goBack">返回</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 响应式数据
const isCollected = ref(false)

// 模拟详情数据
const detail = ref({
  id: 1,
  title: '关于开展2024年疾控医护人员任职资格考试的通知',
  content: `为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。

一、考试时间
2024年3月15日-3月30日

二、考试对象
全市疾控系统医护人员

三、考试内容
1. 疾病预防控制基础知识
2. 流行病学调查方法
3. 实验室检测技术
4. 应急处置能力

四、报名方式
请登录疾控医护考试系统进行在线报名。

五、注意事项
1. 请认真复习相关知识点
2. 考试期间保持网络畅通
3. 如有问题请及时联系技术支持

特此通知。`,
  publishTime: '2024-01-15',
  source: '疾控中心',
  type: 'notice',
  isImportant: true
})

// 页面加载
onLoad((options) => {
  if (options.id) {
    console.log('详情页ID:', options.id)
    // 这里可以根据ID加载具体数据
  }
})

// 分享
const handleShare = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none'
  })
}

// 收藏/取消收藏
const handleCollect = () => {
  isCollected.value = !isCollected.value
  uni.showToast({
    title: isCollected.value ? '收藏成功' : '已取消收藏',
    icon: 'success'
  })
}

// 返回
const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background: #F8F9FA;
  padding: 24rpx;
}

.detail-content {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.article-header {
  padding: 40rpx;

  .header-tags {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 24rpx;

    .important-tag {
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      background: #fff7e6;
      color: #FAAD14;

      text {
        font-weight: bold;
      }
    }
  }

  .article-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #262626;
    line-height: 1.4;
    margin-bottom: 32rpx;
  }

  .article-meta {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .meta-item {
      text {
        font-size: 26rpx;
        color: #595959;
      }
    }
  }
}

.divider {
  height: 2rpx;
  background: #F0F0F0;
  margin: 0 40rpx;
}

.article-body {
  padding: 40rpx;

  .content-text {
    font-size: 30rpx;
    line-height: 1.8;
    color: #262626;
    white-space: pre-line;
  }
}

.article-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 40rpx;
  border-top: 2rpx solid #F0F0F0;

  .action-btn {
    padding: 16rpx 32rpx;
    background: #2E8B57;
    color: #fff;
    border: none;
    border-radius: 8rpx;
    font-size: 26rpx;
  }
}
</style>
