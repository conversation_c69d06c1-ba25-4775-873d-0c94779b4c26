/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-e4e4508d {
  min-height: 100vh;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx 60rpx;
}
.main-content.data-v-e4e4508d {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  max-width: 500rpx;
}
.header.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 100rpx;
}
.header .logo-placeholder.data-v-e4e4508d {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin: 0 auto 40rpx;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.header .logo-placeholder .logo-text.data-v-e4e4508d {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}
.header .app-name.data-v-e4e4508d {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header .app-desc.data-v-e4e4508d {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.login-form.data-v-e4e4508d {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);
}
.agreement-section.data-v-e4e4508d {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}
.agreement-section checkbox.data-v-e4e4508d {
  margin-right: 12rpx;
  margin-top: 4rpx;
}
.agreement-section .agreement-text.data-v-e4e4508d {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
.login-btn.data-v-e4e4508d {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);
  margin-bottom: 40rpx;
}
.login-btn.data-v-e4e4508d:disabled {
  background: #ccc;
  box-shadow: none;
}
.tips.data-v-e4e4508d {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #2E8B57;
}
.tips .tips-text.data-v-e4e4508d {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.footer.data-v-e4e4508d {
  margin-top: 60rpx;
}
.footer .copyright.data-v-e4e4508d {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
}