/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
view.data-v-8bbadf6f, text.data-v-8bbadf6f, button.data-v-8bbadf6f, input.data-v-8bbadf6f, textarea.data-v-8bbadf6f, scroll-view.data-v-8bbadf6f {
  box-sizing: border-box;
}
page.data-v-8bbadf6f {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-8bbadf6f {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-8bbadf6f {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-8bbadf6f {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-8bbadf6f {
  display: flex;
}
.flex-column.data-v-8bbadf6f {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-8bbadf6f {
  flex: 1;
}
.flex-wrap.data-v-8bbadf6f {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-8bbadf6f {
  color: #262626;
}
.text-secondary.data-v-8bbadf6f {
  color: #595959;
}
.text-disabled.data-v-8bbadf6f {
  color: #BFBFBF;
}
.text-success.data-v-8bbadf6f {
  color: #52C41A;
}
.text-warning.data-v-8bbadf6f {
  color: #FAAD14;
}
.text-error.data-v-8bbadf6f {
  color: #F5222D;
}
.text-primary-color.data-v-8bbadf6f {
  color: #2E8B57;
}
.text-center.data-v-8bbadf6f {
  text-align: center;
}
.text-left.data-v-8bbadf6f {
  text-align: left;
}
.text-right.data-v-8bbadf6f {
  text-align: right;
}
.text-bold.data-v-8bbadf6f {
  font-weight: bold;
}
.text-normal.data-v-8bbadf6f {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-8bbadf6f {
  font-size: 20rpx;
}
.text-sm.data-v-8bbadf6f {
  font-size: 24rpx;
}
.text-base.data-v-8bbadf6f {
  font-size: 28rpx;
}
.text-lg.data-v-8bbadf6f {
  font-size: 32rpx;
}
.text-xl.data-v-8bbadf6f {
  font-size: 36rpx;
}
.text-2xl.data-v-8bbadf6f {
  font-size: 40rpx;
}
.text-3xl.data-v-8bbadf6f {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-8bbadf6f {
  margin: 0;
}
.m-1.data-v-8bbadf6f {
  margin: 8rpx;
}
.m-2.data-v-8bbadf6f {
  margin: 16rpx;
}
.m-3.data-v-8bbadf6f {
  margin: 24rpx;
}
.m-4.data-v-8bbadf6f {
  margin: 32rpx;
}
.m-5.data-v-8bbadf6f {
  margin: 40rpx;
}
.mt-0.data-v-8bbadf6f {
  margin-top: 0;
}
.mt-1.data-v-8bbadf6f {
  margin-top: 8rpx;
}
.mt-2.data-v-8bbadf6f {
  margin-top: 16rpx;
}
.mt-3.data-v-8bbadf6f {
  margin-top: 24rpx;
}
.mt-4.data-v-8bbadf6f {
  margin-top: 32rpx;
}
.mt-5.data-v-8bbadf6f {
  margin-top: 40rpx;
}
.mb-0.data-v-8bbadf6f {
  margin-bottom: 0;
}
.mb-1.data-v-8bbadf6f {
  margin-bottom: 8rpx;
}
.mb-2.data-v-8bbadf6f {
  margin-bottom: 16rpx;
}
.mb-3.data-v-8bbadf6f {
  margin-bottom: 24rpx;
}
.mb-4.data-v-8bbadf6f {
  margin-bottom: 32rpx;
}
.mb-5.data-v-8bbadf6f {
  margin-bottom: 40rpx;
}
.ml-0.data-v-8bbadf6f {
  margin-left: 0;
}
.ml-1.data-v-8bbadf6f {
  margin-left: 8rpx;
}
.ml-2.data-v-8bbadf6f {
  margin-left: 16rpx;
}
.ml-3.data-v-8bbadf6f {
  margin-left: 24rpx;
}
.ml-4.data-v-8bbadf6f {
  margin-left: 32rpx;
}
.ml-5.data-v-8bbadf6f {
  margin-left: 40rpx;
}
.mr-0.data-v-8bbadf6f {
  margin-right: 0;
}
.mr-1.data-v-8bbadf6f {
  margin-right: 8rpx;
}
.mr-2.data-v-8bbadf6f {
  margin-right: 16rpx;
}
.mr-3.data-v-8bbadf6f {
  margin-right: 24rpx;
}
.mr-4.data-v-8bbadf6f {
  margin-right: 32rpx;
}
.mr-5.data-v-8bbadf6f {
  margin-right: 40rpx;
}
.p-0.data-v-8bbadf6f {
  padding: 0;
}
.p-1.data-v-8bbadf6f {
  padding: 8rpx;
}
.p-2.data-v-8bbadf6f {
  padding: 16rpx;
}
.p-3.data-v-8bbadf6f {
  padding: 24rpx;
}
.p-4.data-v-8bbadf6f {
  padding: 32rpx;
}
.p-5.data-v-8bbadf6f {
  padding: 40rpx;
}
.pt-0.data-v-8bbadf6f {
  padding-top: 0;
}
.pt-1.data-v-8bbadf6f {
  padding-top: 8rpx;
}
.pt-2.data-v-8bbadf6f {
  padding-top: 16rpx;
}
.pt-3.data-v-8bbadf6f {
  padding-top: 24rpx;
}
.pt-4.data-v-8bbadf6f {
  padding-top: 32rpx;
}
.pt-5.data-v-8bbadf6f {
  padding-top: 40rpx;
}
.pb-0.data-v-8bbadf6f {
  padding-bottom: 0;
}
.pb-1.data-v-8bbadf6f {
  padding-bottom: 8rpx;
}
.pb-2.data-v-8bbadf6f {
  padding-bottom: 16rpx;
}
.pb-3.data-v-8bbadf6f {
  padding-bottom: 24rpx;
}
.pb-4.data-v-8bbadf6f {
  padding-bottom: 32rpx;
}
.pb-5.data-v-8bbadf6f {
  padding-bottom: 40rpx;
}
.pl-0.data-v-8bbadf6f {
  padding-left: 0;
}
.pl-1.data-v-8bbadf6f {
  padding-left: 8rpx;
}
.pl-2.data-v-8bbadf6f {
  padding-left: 16rpx;
}
.pl-3.data-v-8bbadf6f {
  padding-left: 24rpx;
}
.pl-4.data-v-8bbadf6f {
  padding-left: 32rpx;
}
.pl-5.data-v-8bbadf6f {
  padding-left: 40rpx;
}
.pr-0.data-v-8bbadf6f {
  padding-right: 0;
}
.pr-1.data-v-8bbadf6f {
  padding-right: 8rpx;
}
.pr-2.data-v-8bbadf6f {
  padding-right: 16rpx;
}
.pr-3.data-v-8bbadf6f {
  padding-right: 24rpx;
}
.pr-4.data-v-8bbadf6f {
  padding-right: 32rpx;
}
.pr-5.data-v-8bbadf6f {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-8bbadf6f {
  width: 100%;
}
.h-full.data-v-8bbadf6f {
  height: 100%;
}
.w-screen.data-v-8bbadf6f {
  width: 100vw;
}
.h-screen.data-v-8bbadf6f {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-8bbadf6f {
  border-radius: 0;
}
.rounded-sm.data-v-8bbadf6f {
  border-radius: 4rpx;
}
.rounded.data-v-8bbadf6f {
  border-radius: 8rpx;
}
.rounded-lg.data-v-8bbadf6f {
  border-radius: 16rpx;
}
.rounded-xl.data-v-8bbadf6f {
  border-radius: 24rpx;
}
.rounded-full.data-v-8bbadf6f {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-8bbadf6f {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-8bbadf6f {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-8bbadf6f {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-8bbadf6f {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-8bbadf6f {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-8bbadf6f {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-8bbadf6f {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-8bbadf6f {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-8bbadf6f {
  background-color: #2E8B57;
}
.bg-light.data-v-8bbadf6f {
  background-color: #FFFFFF;
}
.bg-gray.data-v-8bbadf6f {
  background-color: #F8F9FA;
}
.bg-success.data-v-8bbadf6f {
  background-color: #52C41A;
}
.bg-warning.data-v-8bbadf6f {
  background-color: #FAAD14;
}
.bg-error.data-v-8bbadf6f {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-8bbadf6f {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-8bbadf6f {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-8bbadf6f {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-8bbadf6f {
  color: #FAAD14;
}
.status-approved.data-v-8bbadf6f {
  color: #52C41A;
}
.status-rejected.data-v-8bbadf6f {
  color: #F5222D;
}
.status-not-submitted.data-v-8bbadf6f {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-8bbadf6f {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-8bbadf6f {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-8bbadf6f {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-8bbadf6f {
  animation: fadeIn-8bbadf6f 0.3s ease-in-out;
}
@keyframes fadeIn-8bbadf6f {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-8bbadf6f {
  animation: slideUp-8bbadf6f 0.3s ease-out;
}
@keyframes slideUp-8bbadf6f {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-8bbadf6f::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-8bbadf6f {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-8bbadf6f,
.fade-leave-active.data-v-8bbadf6f {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-8bbadf6f,
.fade-leave-to.data-v-8bbadf6f {
  opacity: 0;
}
.slide-up-enter-active.data-v-8bbadf6f,
.slide-up-leave-active.data-v-8bbadf6f {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-8bbadf6f,
.slide-up-leave-to.data-v-8bbadf6f {
  transform: translateY(100%);
}
.certificate-container.data-v-8bbadf6f {
  min-height: 100vh;
  background: #F8F9FA;
}
.stats-section.data-v-8bbadf6f {
  margin: 24rpx;
}
.stats-section .stats-card.data-v-8bbadf6f {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}
.stats-section .stats-card .stat-item.data-v-8bbadf6f {
  flex: 1;
  text-align: center;
}
.stats-section .stats-card .stat-item .stat-number.data-v-8bbadf6f {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2E8B57;
  margin-bottom: 8rpx;
}
.stats-section .stats-card .stat-item .stat-label.data-v-8bbadf6f {
  font-size: 24rpx;
  color: #666;
}
.stats-section .stats-card .stat-divider.data-v-8bbadf6f {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}
.filter-section.data-v-8bbadf6f {
  margin: 0 24rpx 24rpx;
}
.filter-section .filter-tabs.data-v-8bbadf6f {
  background: #fff;
  border-radius: 16rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.filter-section .filter-tabs .filter-tab.data-v-8bbadf6f {
  flex: 1;
  text-align: center;
  padding: 16rpx 8rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.filter-section .filter-tabs .filter-tab.active.data-v-8bbadf6f {
  background: #2E8B57;
}
.filter-section .filter-tabs .filter-tab.active text.data-v-8bbadf6f {
  color: #fff;
  font-weight: bold;
}
.filter-section .filter-tabs .filter-tab text.data-v-8bbadf6f {
  font-size: 26rpx;
  color: #666;
}
.certificate-list.data-v-8bbadf6f {
  padding: 0 24rpx;
}
.certificate-list .certificate-item.data-v-8bbadf6f {
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.certificate-list .certificate-item .certificate-header.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.certificate-list .certificate-item .certificate-header .certificate-icon.data-v-8bbadf6f {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.certificate-list .certificate-item .certificate-header .certificate-info.data-v-8bbadf6f {
  flex: 1;
}
.certificate-list .certificate-item .certificate-header .certificate-info .certificate-name.data-v-8bbadf6f {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.certificate-list .certificate-item .certificate-header .certificate-info .certificate-type.data-v-8bbadf6f {
  font-size: 24rpx;
  color: #666;
}
.certificate-list .certificate-item .certificate-content .certificate-details.data-v-8bbadf6f {
  margin-bottom: 24rpx;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item.data-v-8bbadf6f:last-child {
  margin-bottom: 0;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-label.data-v-8bbadf6f {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #666;
  min-width: 140rpx;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-value.data-v-8bbadf6f {
  font-size: 24rpx;
  color: #333;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-value.score.data-v-8bbadf6f {
  color: #2E8B57;
  font-weight: bold;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-value.normal.data-v-8bbadf6f {
  color: #333;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-value.expiring-soon.data-v-8bbadf6f {
  color: #FF9500;
}
.certificate-list .certificate-item .certificate-content .certificate-details .detail-item .detail-value.expired.data-v-8bbadf6f {
  color: #f56c6c;
}
.certificate-list .certificate-item .certificate-content .certificate-actions.data-v-8bbadf6f {
  display: flex;
  gap: 16rpx;
}
.certificate-list .certificate-item .certificate-content .certificate-actions .view-btn.data-v-8bbadf6f,
.certificate-list .certificate-item .certificate-content .certificate-actions .download-btn.data-v-8bbadf6f,
.certificate-list .certificate-item .certificate-content .certificate-actions .share-btn.data-v-8bbadf6f {
  flex: 1;
  height: 64rpx;
  border-radius: 32rpx;
}
.certificate-detail-modal.data-v-8bbadf6f {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.certificate-detail-modal .modal-header.data-v-8bbadf6f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.certificate-detail-modal .modal-header .modal-title.data-v-8bbadf6f {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.certificate-detail-modal .modal-content.data-v-8bbadf6f {
  flex: 1;
  padding: 40rpx;
}
.certificate-detail-modal .certificate-preview.data-v-8bbadf6f {
  margin-bottom: 40rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame.data-v-8bbadf6f {
  border: 8rpx solid #FFD700;
  border-radius: 16rpx;
  overflow: hidden;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg.data-v-8bbadf6f {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 60rpx 40rpx;
  text-align: center;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-title.data-v-8bbadf6f {
  margin-bottom: 40rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-title text.data-v-8bbadf6f {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E8B57;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-recipient.data-v-8bbadf6f {
  margin-bottom: 40rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-recipient text.data-v-8bbadf6f {
  font-size: 28rpx;
  color: #333;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-recipient text.recipient-name.data-v-8bbadf6f {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E8B57;
  margin: 0 16rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-content-text.data-v-8bbadf6f {
  margin-bottom: 40rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-content-text text.data-v-8bbadf6f {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-footer .issue-info.data-v-8bbadf6f {
  margin-bottom: 20rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-footer .issue-info text.data-v-8bbadf6f {
  display: block;
  font-size: 20rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.certificate-detail-modal .certificate-preview .certificate-frame .certificate-bg .certificate-footer .certificate-number text.data-v-8bbadf6f {
  font-size: 20rpx;
  color: #999;
}
.certificate-detail-modal .certificate-info-detail .info-item.data-v-8bbadf6f {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.certificate-detail-modal .certificate-info-detail .info-item.data-v-8bbadf6f:last-child {
  margin-bottom: 0;
}
.certificate-detail-modal .certificate-info-detail .info-item .info-label.data-v-8bbadf6f {
  width: 140rpx;
  font-size: 26rpx;
  color: #666;
}
.certificate-detail-modal .certificate-info-detail .info-item .info-value.data-v-8bbadf6f {
  font-size: 26rpx;
  color: #333;
}
.certificate-detail-modal .modal-footer.data-v-8bbadf6f {
  padding: 40rpx;
  border-top: 2rpx solid #f0f0f0;
}
.certificate-detail-modal .modal-footer .download-modal-btn.data-v-8bbadf6f {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
}