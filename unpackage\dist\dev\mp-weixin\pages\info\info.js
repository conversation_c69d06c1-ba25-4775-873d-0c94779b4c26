"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "info",
  setup(__props) {
    const currentTab = common_vendor.ref(0);
    const tabList = [
      { name: "全部", key: "all" },
      { name: "公告", key: "notice" },
      { name: "政策法规", key: "policy" },
      { name: "重要通知", key: "news" }
    ];
    const mockData = [
      {
        id: 1,
        title: "关于开展2024年疾控医护人员任职资格考试的通知",
        content: "为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。",
        publishTime: "2024-01-15",
        source: "疾控中心",
        type: "notice",
        isImportant: true
      },
      {
        id: 2,
        title: "疾控医护人员继续教育管理办法",
        content: "为加强疾控医护人员继续教育管理，提高专业技术水平，制定本办法。",
        publishTime: "2024-01-10",
        source: "卫健委",
        type: "policy",
        isImportant: false
      },
      {
        id: 3,
        title: "考试系统使用指南",
        content: "本指南详细介绍了疾控医护考试系统的使用方法，包括注册、登录、考试等功能。",
        publishTime: "2024-01-08",
        source: "技术部",
        type: "news",
        isImportant: false
      }
    ];
    const allList = common_vendor.ref(mockData);
    const currentList = common_vendor.computed(() => {
      const currentTabKey = tabList[currentTab.value].key;
      if (currentTabKey === "all") {
        return allList.value;
      }
      return allList.value.filter((item) => item.type === currentTabKey);
    });
    const onTabChange = (index) => {
      currentTab.value = index;
    };
    const onLoadMore = () => {
      common_vendor.index.__f__("log", "at pages/info/info.vue:142", "加载更多");
    };
    const viewDetail = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/info/detail?id=${item.id}`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(tabList, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.name),
            b: tab.key,
            c: currentTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => onTabChange(index), tab.key)
          };
        }),
        b: currentList.value.length > 0
      }, currentList.value.length > 0 ? {
        c: common_vendor.f(currentList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: item.isImportant
          }, item.isImportant ? {} : {}, {
            c: common_vendor.t(item.content),
            d: common_vendor.t(item.publishTime),
            e: common_vendor.t(item.source),
            f: item.id,
            g: common_vendor.o(($event) => viewDetail(item), item.id)
          });
        })
      } : {}, {
        d: common_vendor.o(onLoadMore)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f52d2d81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/info/info.js.map
