{"version": 3, "file": "index.js", "sources": ["src/utils/index.ts"], "sourcesContent": ["import { REGEX } from '../constants'\n\n/**\n * 格式化日期\n * @param date 日期\n * @param format 格式 默认 'YYYY-MM-DD HH:mm:ss'\n */\nexport function formatDate(date: Date | string | number, format: string = 'YYYY-MM-DD HH:mm:ss'): string {\n  const d = new Date(date)\n  \n  if (isNaN(d.getTime())) {\n    return ''\n  }\n\n  const year = d.getFullYear()\n  const month = String(d.getMonth() + 1).padStart(2, '0')\n  const day = String(d.getDate()).padStart(2, '0')\n  const hours = String(d.getHours()).padStart(2, '0')\n  const minutes = String(d.getMinutes()).padStart(2, '0')\n  const seconds = String(d.getSeconds()).padStart(2, '0')\n\n  return format\n    .replace('YYYY', String(year))\n    .replace('MM', month)\n    .replace('DD', day)\n    .replace('HH', hours)\n    .replace('mm', minutes)\n    .replace('ss', seconds)\n}\n\n/**\n * 格式化相对时间\n * @param date 日期\n */\nexport function formatRelativeTime(date: Date | string | number): string {\n  const now = new Date()\n  const target = new Date(date)\n  const diff = now.getTime() - target.getTime()\n\n  const minute = 60 * 1000\n  const hour = 60 * minute\n  const day = 24 * hour\n  const week = 7 * day\n  const month = 30 * day\n\n  if (diff < minute) {\n    return '刚刚'\n  } else if (diff < hour) {\n    return `${Math.floor(diff / minute)}分钟前`\n  } else if (diff < day) {\n    return `${Math.floor(diff / hour)}小时前`\n  } else if (diff < week) {\n    return `${Math.floor(diff / day)}天前`\n  } else if (diff < month) {\n    return `${Math.floor(diff / week)}周前`\n  } else {\n    return formatDate(target, 'YYYY-MM-DD')\n  }\n}\n\n/**\n * 格式化时长\n * @param seconds 秒数\n */\nexport function formatDuration(seconds: number): string {\n  const hours = Math.floor(seconds / 3600)\n  const minutes = Math.floor((seconds % 3600) / 60)\n  const secs = seconds % 60\n\n  if (hours > 0) {\n    return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`\n  } else {\n    return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`\n  }\n}\n\n/**\n * 验证手机号\n * @param phone 手机号\n */\nexport function validatePhone(phone: string): boolean {\n  return REGEX.PHONE.test(phone)\n}\n\n/**\n * 验证身份证号\n * @param idCard 身份证号\n */\nexport function validateIdCard(idCard: string): boolean {\n  return REGEX.ID_CARD.test(idCard)\n}\n\n/**\n * 验证邮箱\n * @param email 邮箱\n */\nexport function validateEmail(email: string): boolean {\n  return REGEX.EMAIL.test(email)\n}\n\n/**\n * 脱敏手机号\n * @param phone 手机号\n */\nexport function maskPhone(phone: string): string {\n  if (!phone || phone.length !== 11) return phone\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2')\n}\n\n/**\n * 脱敏身份证号\n * @param idCard 身份证号\n */\nexport function maskIdCard(idCard: string): string {\n  if (!idCard || idCard.length !== 18) return idCard\n  return idCard.replace(/(\\d{6})\\d{8}(\\d{4})/, '$1********$2')\n}\n\n/**\n * 防抖函数\n * @param func 函数\n * @param delay 延迟时间\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  delay: number\n): (...args: Parameters<T>) => void {\n  let timeoutId: number | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId)\n    }\n    \n    timeoutId = setTimeout(() => {\n      func(...args)\n    }, delay)\n  }\n}\n\n/**\n * 节流函数\n * @param func 函数\n * @param delay 延迟时间\n */\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  delay: number\n): (...args: Parameters<T>) => void {\n  let lastCall = 0\n  \n  return (...args: Parameters<T>) => {\n    const now = Date.now()\n    \n    if (now - lastCall >= delay) {\n      lastCall = now\n      func(...args)\n    }\n  }\n}\n\n/**\n * 深拷贝\n * @param obj 对象\n */\nexport function deepClone<T>(obj: T): T {\n  if (obj === null || typeof obj !== 'object') {\n    return obj\n  }\n\n  if (obj instanceof Date) {\n    return new Date(obj.getTime()) as T\n  }\n\n  if (obj instanceof Array) {\n    return obj.map(item => deepClone(item)) as T\n  }\n\n  if (typeof obj === 'object') {\n    const cloned = {} as T\n    for (const key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        cloned[key] = deepClone(obj[key])\n      }\n    }\n    return cloned\n  }\n\n  return obj\n}\n\n/**\n * 生成随机字符串\n * @param length 长度\n */\nexport function generateRandomString(length: number = 8): string {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n  let result = ''\n  \n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length))\n  }\n  \n  return result\n}\n\n/**\n * 获取文件扩展名\n * @param filename 文件名\n */\nexport function getFileExtension(filename: string): string {\n  return filename.slice(((filename.lastIndexOf('.') - 1) >>> 0) + 2)\n}\n\n/**\n * 格式化文件大小\n * @param bytes 字节数\n */\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 B'\n  \n  const k = 1024\n  const sizes = ['B', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n/**\n * 检查是否为空值\n * @param value 值\n */\nexport function isEmpty(value: any): boolean {\n  if (value === null || value === undefined) return true\n  if (typeof value === 'string') return value.trim() === ''\n  if (Array.isArray(value)) return value.length === 0\n  if (typeof value === 'object') return Object.keys(value).length === 0\n  return false\n}\n\n/**\n * 获取URL参数\n * @param url URL\n */\nexport function getUrlParams(url: string): Record<string, string> {\n  const params: Record<string, string> = {}\n  const urlObj = new URL(url)\n  \n  urlObj.searchParams.forEach((value, key) => {\n    params[key] = value\n  })\n  \n  return params\n}\n\n/**\n * 数组去重\n * @param array 数组\n * @param key 对象数组的去重键\n */\nexport function uniqueArray<T>(array: T[], key?: keyof T): T[] {\n  if (!key) {\n    return [...new Set(array)]\n  }\n  \n  const seen = new Set()\n  return array.filter(item => {\n    const value = item[key]\n    if (seen.has(value)) {\n      return false\n    }\n    seen.add(value)\n    return true\n  })\n}\n\n/**\n * 数组分组\n * @param array 数组\n * @param key 分组键\n */\nexport function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {\n  return array.reduce((groups, item) => {\n    const group = String(item[key])\n    if (!groups[group]) {\n      groups[group] = []\n    }\n    groups[group].push(item)\n    return groups\n  }, {} as Record<string, T[]>)\n}\n\n/**\n * 延迟执行\n * @param ms 毫秒\n */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\n/**\n * 重试函数\n * @param fn 函数\n * @param retries 重试次数\n * @param delay 延迟时间\n */\nexport async function retry<T>(\n  fn: () => Promise<T>,\n  retries: number = 3,\n  delay: number = 1000\n): Promise<T> {\n  try {\n    return await fn()\n  } catch (error) {\n    if (retries > 0) {\n      await sleep(delay)\n      return retry(fn, retries - 1, delay)\n    }\n    throw error\n  }\n}\n"], "names": ["REGEX"], "mappings": ";;AAOgB,SAAA,WAAW,MAA8B,SAAiB,uBAA+B;AACjG,QAAA,IAAI,IAAI,KAAK,IAAI;AAEvB,MAAI,MAAM,EAAE,QAAQ,CAAC,GAAG;AACf,WAAA;AAAA,EACT;AAEM,QAAA,OAAO,EAAE;AACT,QAAA,QAAQ,OAAO,EAAE,SAAA,IAAa,CAAC,EAAE,SAAS,GAAG,GAAG;AAChD,QAAA,MAAM,OAAO,EAAE,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AACzC,QAAA,QAAQ,OAAO,EAAE,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AAC5C,QAAA,UAAU,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAChD,QAAA,UAAU,OAAO,EAAE,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAE/C,SAAA,OACJ,QAAQ,QAAQ,OAAO,IAAI,CAAC,EAC5B,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,GAAG,EACjB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,OAAO;AAC1B;AAoCO,SAAS,eAAe,SAAyB;AACtD,QAAM,QAAQ,KAAK,MAAM,UAAU,IAAI;AACvC,QAAM,UAAU,KAAK,MAAO,UAAU,OAAQ,EAAE;AAChD,QAAM,OAAO,UAAU;AAEvB,MAAI,QAAQ,GAAG;AACb,WAAO,GAAG,KAAK,IAAI,OAAO,OAAO,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,EAAA,OAC/E;AACL,WAAO,GAAG,OAAO,OAAO,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,IAAI,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,EAC7E;AACF;AAMO,SAAS,cAAc,OAAwB;AAC7C,SAAAA,0BAAM,MAAM,KAAK,KAAK;AAC/B;AAMO,SAAS,eAAe,QAAyB;AAC/C,SAAAA,0BAAM,QAAQ,KAAK,MAAM;AAClC;AAuBO,SAAS,WAAW,QAAwB;AAC7C,MAAA,CAAC,UAAU,OAAO,WAAW;AAAW,WAAA;AACrC,SAAA,OAAO,QAAQ,uBAAuB,cAAc;AAC7D;;;;;;"}