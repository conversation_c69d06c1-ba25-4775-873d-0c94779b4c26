{"version": 3, "file": "detail.js", "sources": ["pages/info/detail.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5mby9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 详情内容 -->\n    <view class=\"detail-content\">\n      <!-- 文章头部 -->\n      <view class=\"article-header\">\n        <view class=\"header-tags\">\n          <view v-if=\"detail.isImportant\" class=\"important-tag\">\n            <text>重要</text>\n          </view>\n        </view>\n\n        <text class=\"article-title\">{{ detail.title }}</text>\n\n        <view class=\"article-meta\">\n          <view class=\"meta-item\">\n            <text>📅 {{ detail.publishTime }}</text>\n          </view>\n          <view v-if=\"detail.source\" class=\"meta-item\">\n            <text>🏢 {{ detail.source }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 分割线 -->\n      <view class=\"divider\"></view>\n\n      <!-- 文章内容 -->\n      <view class=\"article-body\">\n        <text class=\"content-text\">{{ detail.content }}</text>\n      </view>\n\n      <!-- 底部操作 -->\n      <view class=\"article-actions\">\n        <button class=\"action-btn\" @click=\"handleShare\">分享</button>\n        <button class=\"action-btn\" @click=\"handleCollect\">\n          {{ isCollected ? '已收藏' : '收藏' }}\n        </button>\n        <button class=\"action-btn\" @click=\"goBack\">返回</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref } from 'vue'\nimport { onLoad } from '@dcloudio/uni-app'\n\n// 响应式数据\nconst isCollected = ref(false)\n\n// 模拟详情数据\nconst detail = ref({\n  id: 1,\n  title: '关于开展2024年疾控医护人员任职资格考试的通知',\n  content: `为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。\n\n一、考试时间\n2024年3月15日-3月30日\n\n二、考试对象\n全市疾控系统医护人员\n\n三、考试内容\n1. 疾病预防控制基础知识\n2. 流行病学调查方法\n3. 实验室检测技术\n4. 应急处置能力\n\n四、报名方式\n请登录疾控医护考试系统进行在线报名。\n\n五、注意事项\n1. 请认真复习相关知识点\n2. 考试期间保持网络畅通\n3. 如有问题请及时联系技术支持\n\n特此通知。`,\n  publishTime: '2024-01-15',\n  source: '疾控中心',\n  type: 'notice',\n  isImportant: true\n})\n\n// 页面加载\nonLoad((options) => {\n  if (options.id) {\n    uni.__f__('log','at pages/info/detail.vue:88','详情页ID:', options.id)\n    // 这里可以根据ID加载具体数据\n  }\n})\n\n// 分享\nconst handleShare = () => {\n  uni.showToast({\n    title: '分享功能开发中',\n    icon: 'none'\n  })\n}\n\n// 收藏/取消收藏\nconst handleCollect = () => {\n  isCollected.value = !isCollected.value\n  uni.showToast({\n    title: isCollected.value ? '收藏成功' : '已取消收藏',\n    icon: 'success'\n  })\n}\n\n// 返回\nconst goBack = () => {\n  uni.navigateBack()\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.detail-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n  padding: 24rpx;\n}\n\n.detail-content {\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.article-header {\n  padding: 40rpx;\n\n  .header-tags {\n    display: flex;\n    align-items: center;\n    gap: 16rpx;\n    margin-bottom: 24rpx;\n\n    .important-tag {\n      padding: 8rpx 16rpx;\n      border-radius: 20rpx;\n      font-size: 24rpx;\n      background: #fff7e6;\n      color: #FAAD14;\n\n      text {\n        font-weight: bold;\n      }\n    }\n  }\n\n  .article-title {\n    display: block;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #262626;\n    line-height: 1.4;\n    margin-bottom: 32rpx;\n  }\n\n  .article-meta {\n    display: flex;\n    flex-direction: column;\n    gap: 16rpx;\n\n    .meta-item {\n      text {\n        font-size: 26rpx;\n        color: #595959;\n      }\n    }\n  }\n}\n\n.divider {\n  height: 2rpx;\n  background: #F0F0F0;\n  margin: 0 40rpx;\n}\n\n.article-body {\n  padding: 40rpx;\n\n  .content-text {\n    font-size: 30rpx;\n    line-height: 1.8;\n    color: #262626;\n    white-space: pre-line;\n  }\n}\n\n.article-actions {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  padding: 32rpx 40rpx;\n  border-top: 2rpx solid #F0F0F0;\n\n  .action-btn {\n    padding: 16rpx 32rpx;\n    background: #2E8B57;\n    color: #fff;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 26rpx;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/info/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni"], "mappings": ";;;;;AAiDM,UAAA,cAAcA,kBAAI,KAAK;AAG7B,UAAM,SAASA,cAAAA,IAAI;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBT,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,IAAA,CACd;AAGDC,kBAAA,OAAO,CAAC,YAAY;AAClB,UAAI,QAAQ,IAAI;AACdC,sBAAA,MAAI,MAAM,OAAM,+BAA8B,UAAU,QAAQ,EAAE;AAAA,MAEpE;AAAA,IAAA,CACD;AAGD,UAAM,cAAc,MAAM;AACxBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,gBAAgB,MAAM;AACd,kBAAA,QAAQ,CAAC,YAAY;AACjCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,YAAY,QAAQ,SAAS;AAAA,QACpC,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,SAAS,MAAM;AACnBA,oBAAA,MAAI,aAAa;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;AC9GnB,GAAG,WAAW,eAAe;"}