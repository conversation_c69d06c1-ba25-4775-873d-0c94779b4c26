/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
view.data-v-ba6cefc0, text.data-v-ba6cefc0, button.data-v-ba6cefc0, input.data-v-ba6cefc0, textarea.data-v-ba6cefc0, scroll-view.data-v-ba6cefc0 {
  box-sizing: border-box;
}
page.data-v-ba6cefc0 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-ba6cefc0 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-ba6cefc0 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-ba6cefc0 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-ba6cefc0 {
  display: flex;
}
.flex-column.data-v-ba6cefc0 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-ba6cefc0 {
  flex: 1;
}
.flex-wrap.data-v-ba6cefc0 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-ba6cefc0 {
  color: #262626;
}
.text-secondary.data-v-ba6cefc0 {
  color: #595959;
}
.text-disabled.data-v-ba6cefc0 {
  color: #BFBFBF;
}
.text-success.data-v-ba6cefc0 {
  color: #52C41A;
}
.text-warning.data-v-ba6cefc0 {
  color: #FAAD14;
}
.text-error.data-v-ba6cefc0 {
  color: #F5222D;
}
.text-primary-color.data-v-ba6cefc0 {
  color: #2E8B57;
}
.text-center.data-v-ba6cefc0 {
  text-align: center;
}
.text-left.data-v-ba6cefc0 {
  text-align: left;
}
.text-right.data-v-ba6cefc0 {
  text-align: right;
}
.text-bold.data-v-ba6cefc0 {
  font-weight: bold;
}
.text-normal.data-v-ba6cefc0 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-ba6cefc0 {
  font-size: 20rpx;
}
.text-sm.data-v-ba6cefc0 {
  font-size: 24rpx;
}
.text-base.data-v-ba6cefc0 {
  font-size: 28rpx;
}
.text-lg.data-v-ba6cefc0 {
  font-size: 32rpx;
}
.text-xl.data-v-ba6cefc0 {
  font-size: 36rpx;
}
.text-2xl.data-v-ba6cefc0 {
  font-size: 40rpx;
}
.text-3xl.data-v-ba6cefc0 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-ba6cefc0 {
  margin: 0;
}
.m-1.data-v-ba6cefc0 {
  margin: 8rpx;
}
.m-2.data-v-ba6cefc0 {
  margin: 16rpx;
}
.m-3.data-v-ba6cefc0 {
  margin: 24rpx;
}
.m-4.data-v-ba6cefc0 {
  margin: 32rpx;
}
.m-5.data-v-ba6cefc0 {
  margin: 40rpx;
}
.mt-0.data-v-ba6cefc0 {
  margin-top: 0;
}
.mt-1.data-v-ba6cefc0 {
  margin-top: 8rpx;
}
.mt-2.data-v-ba6cefc0 {
  margin-top: 16rpx;
}
.mt-3.data-v-ba6cefc0 {
  margin-top: 24rpx;
}
.mt-4.data-v-ba6cefc0 {
  margin-top: 32rpx;
}
.mt-5.data-v-ba6cefc0 {
  margin-top: 40rpx;
}
.mb-0.data-v-ba6cefc0 {
  margin-bottom: 0;
}
.mb-1.data-v-ba6cefc0 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-ba6cefc0 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-ba6cefc0 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-ba6cefc0 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-ba6cefc0 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-ba6cefc0 {
  margin-left: 0;
}
.ml-1.data-v-ba6cefc0 {
  margin-left: 8rpx;
}
.ml-2.data-v-ba6cefc0 {
  margin-left: 16rpx;
}
.ml-3.data-v-ba6cefc0 {
  margin-left: 24rpx;
}
.ml-4.data-v-ba6cefc0 {
  margin-left: 32rpx;
}
.ml-5.data-v-ba6cefc0 {
  margin-left: 40rpx;
}
.mr-0.data-v-ba6cefc0 {
  margin-right: 0;
}
.mr-1.data-v-ba6cefc0 {
  margin-right: 8rpx;
}
.mr-2.data-v-ba6cefc0 {
  margin-right: 16rpx;
}
.mr-3.data-v-ba6cefc0 {
  margin-right: 24rpx;
}
.mr-4.data-v-ba6cefc0 {
  margin-right: 32rpx;
}
.mr-5.data-v-ba6cefc0 {
  margin-right: 40rpx;
}
.p-0.data-v-ba6cefc0 {
  padding: 0;
}
.p-1.data-v-ba6cefc0 {
  padding: 8rpx;
}
.p-2.data-v-ba6cefc0 {
  padding: 16rpx;
}
.p-3.data-v-ba6cefc0 {
  padding: 24rpx;
}
.p-4.data-v-ba6cefc0 {
  padding: 32rpx;
}
.p-5.data-v-ba6cefc0 {
  padding: 40rpx;
}
.pt-0.data-v-ba6cefc0 {
  padding-top: 0;
}
.pt-1.data-v-ba6cefc0 {
  padding-top: 8rpx;
}
.pt-2.data-v-ba6cefc0 {
  padding-top: 16rpx;
}
.pt-3.data-v-ba6cefc0 {
  padding-top: 24rpx;
}
.pt-4.data-v-ba6cefc0 {
  padding-top: 32rpx;
}
.pt-5.data-v-ba6cefc0 {
  padding-top: 40rpx;
}
.pb-0.data-v-ba6cefc0 {
  padding-bottom: 0;
}
.pb-1.data-v-ba6cefc0 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-ba6cefc0 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-ba6cefc0 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-ba6cefc0 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-ba6cefc0 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-ba6cefc0 {
  padding-left: 0;
}
.pl-1.data-v-ba6cefc0 {
  padding-left: 8rpx;
}
.pl-2.data-v-ba6cefc0 {
  padding-left: 16rpx;
}
.pl-3.data-v-ba6cefc0 {
  padding-left: 24rpx;
}
.pl-4.data-v-ba6cefc0 {
  padding-left: 32rpx;
}
.pl-5.data-v-ba6cefc0 {
  padding-left: 40rpx;
}
.pr-0.data-v-ba6cefc0 {
  padding-right: 0;
}
.pr-1.data-v-ba6cefc0 {
  padding-right: 8rpx;
}
.pr-2.data-v-ba6cefc0 {
  padding-right: 16rpx;
}
.pr-3.data-v-ba6cefc0 {
  padding-right: 24rpx;
}
.pr-4.data-v-ba6cefc0 {
  padding-right: 32rpx;
}
.pr-5.data-v-ba6cefc0 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-ba6cefc0 {
  width: 100%;
}
.h-full.data-v-ba6cefc0 {
  height: 100%;
}
.w-screen.data-v-ba6cefc0 {
  width: 100vw;
}
.h-screen.data-v-ba6cefc0 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-ba6cefc0 {
  border-radius: 0;
}
.rounded-sm.data-v-ba6cefc0 {
  border-radius: 4rpx;
}
.rounded.data-v-ba6cefc0 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-ba6cefc0 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-ba6cefc0 {
  border-radius: 24rpx;
}
.rounded-full.data-v-ba6cefc0 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-ba6cefc0 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-ba6cefc0 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-ba6cefc0 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-ba6cefc0 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-ba6cefc0 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-ba6cefc0 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-ba6cefc0 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-ba6cefc0 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-ba6cefc0 {
  background-color: #2E8B57;
}
.bg-light.data-v-ba6cefc0 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-ba6cefc0 {
  background-color: #F8F9FA;
}
.bg-success.data-v-ba6cefc0 {
  background-color: #52C41A;
}
.bg-warning.data-v-ba6cefc0 {
  background-color: #FAAD14;
}
.bg-error.data-v-ba6cefc0 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-ba6cefc0 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-ba6cefc0 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-ba6cefc0 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-ba6cefc0 {
  color: #FAAD14;
}
.status-approved.data-v-ba6cefc0 {
  color: #52C41A;
}
.status-rejected.data-v-ba6cefc0 {
  color: #F5222D;
}
.status-not-submitted.data-v-ba6cefc0 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-ba6cefc0 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-ba6cefc0 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-ba6cefc0 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-ba6cefc0 {
  animation: fadeIn-ba6cefc0 0.3s ease-in-out;
}
@keyframes fadeIn-ba6cefc0 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-ba6cefc0 {
  animation: slideUp-ba6cefc0 0.3s ease-out;
}
@keyframes slideUp-ba6cefc0 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-ba6cefc0::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-ba6cefc0 {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-ba6cefc0,
.fade-leave-active.data-v-ba6cefc0 {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-ba6cefc0,
.fade-leave-to.data-v-ba6cefc0 {
  opacity: 0;
}
.slide-up-enter-active.data-v-ba6cefc0,
.slide-up-leave-active.data-v-ba6cefc0 {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-ba6cefc0,
.slide-up-leave-to.data-v-ba6cefc0 {
  transform: translateY(100%);
}
.category-container.data-v-ba6cefc0 {
  min-height: 100vh;
  background: #F8F9FA;
}
.practice-limit-banner.data-v-ba6cefc0 {
  margin: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.practice-limit-banner .banner-content.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.practice-limit-banner .banner-content .banner-text.data-v-ba6cefc0 {
  flex: 1;
}
.practice-limit-banner .banner-content .banner-text .limit-text.data-v-ba6cefc0 {
  font-size: 26rpx;
}
.practice-limit-banner .banner-content .banner-text .limit-text.unlimited.data-v-ba6cefc0 {
  color: #4CAF50;
  font-weight: bold;
}
.practice-limit-banner .banner-content .banner-text .limit-text.limited.data-v-ba6cefc0 {
  color: #f56c6c;
}
.category-list.data-v-ba6cefc0 {
  padding: 24rpx;
}
.category-list .category-card.data-v-ba6cefc0 {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.category-list .category-card .card-header.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  padding: 40rpx;
}
.category-list .category-card .card-header .category-icon.data-v-ba6cefc0 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}
.category-list .category-card .card-header .category-info.data-v-ba6cefc0 {
  flex: 1;
}
.category-list .category-card .card-header .category-info .category-name.data-v-ba6cefc0 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.category-list .category-card .card-header .category-info .category-desc.data-v-ba6cefc0 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.category-list .category-card .card-header .category-meta.data-v-ba6cefc0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.category-list .category-card .card-header .category-meta .question-count.data-v-ba6cefc0 {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: bold;
}
.category-list .category-card .card-footer.data-v-ba6cefc0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 40rpx;
  background: #f8f9fa;
  border-top: 2rpx solid #f0f0f0;
}
.category-list .category-card .card-footer .difficulty-tags.data-v-ba6cefc0 {
  display: flex;
  gap: 12rpx;
}
.category-list .category-card .card-footer .difficulty-tags .tag.data-v-ba6cefc0 {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}
.category-list .category-card .card-footer .difficulty-tags .tag.easy.data-v-ba6cefc0 {
  background: #f6ffed;
  color: #4CAF50;
  border: 1rpx solid #b7eb8f;
}
.category-list .category-card .card-footer .difficulty-tags .tag.medium.data-v-ba6cefc0 {
  background: #fff7e6;
  color: #FF9500;
  border: 1rpx solid #ffd591;
}
.category-list .category-card .card-footer .difficulty-tags .tag.hard.data-v-ba6cefc0 {
  background: #fff2f0;
  color: #f56c6c;
  border: 1rpx solid #ffccc7;
}
.category-list .category-card .card-footer .practice-info .practice-text.data-v-ba6cefc0 {
  font-size: 24rpx;
  color: #4A90E2;
  font-weight: bold;
}
.bottom-tips.data-v-ba6cefc0 {
  margin: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}
.bottom-tips .tips-content.data-v-ba6cefc0 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.bottom-tips .tips-content .tips-text.data-v-ba6cefc0 {
  flex: 1;
}
.bottom-tips .tips-content .tips-text .tips-title.data-v-ba6cefc0 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.bottom-tips .tips-content .tips-text .tips-desc.data-v-ba6cefc0 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}