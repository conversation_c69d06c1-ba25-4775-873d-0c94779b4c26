<template>
  <view class="info-center-container">
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">信息中心</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 轮播公告 -->
      <view class="banner-section">
        <view class="banner-item">
          <view class="banner-content">
            <text class="banner-title">欢迎使用疾控医护考试系统</text>
            <text class="banner-desc">为疾控医护人员提供专业的考试学习平台</text>
          </view>
          <view class="banner-tag important">重要</view>
        </view>
      </view>

      <!-- 内容分类 -->
      <view class="content-categories">
        <view class="tab-list">
          <view
            v-for="(tab, index) in tabList"
            :key="tab.key"
            class="tab-item"
            :class="{ active: currentTab === index }"
            @click="onTabChange(index)"
          >
            <text>{{ tab.name }}</text>
          </view>
        </view>
      </view>

      <!-- 内容列表 -->
      <view class="content-list">
        <scroll-view
          class="list-scroll"
          scroll-y
          @scrolltolower="onLoadMore"
        >
          <!-- 内容列表 -->
          <view v-if="currentList.length > 0">
            <view
              v-for="item in currentList"
              :key="item.id"
              class="content-item"
              @click="viewDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
                <view v-if="item.isImportant" class="important-tag">
                  <text>重要</text>
                </view>
              </view>

              <text class="item-summary">{{ item.content }}</text>

              <view class="item-footer">
                <text class="item-date">{{ item.publishTime }}</text>
                <text class="item-source">{{ item.source }}</text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <text class="empty-text">暂无内容</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 数据状态
const currentTab = ref(0)

// Tab配置
const tabList = [
  { name: '全部', key: 'all' },
  { name: '公告', key: 'notice' },
  { name: '政策法规', key: 'policy' },
  { name: '重要通知', key: 'news' }
]

// 模拟数据
const mockData = [
  {
    id: 1,
    title: '关于开展2024年疾控医护人员任职资格考试的通知',
    content: '为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。',
    publishTime: '2024-01-15',
    source: '疾控中心',
    type: 'notice',
    isImportant: true
  },
  {
    id: 2,
    title: '疾控医护人员继续教育管理办法',
    content: '为加强疾控医护人员继续教育管理，提高专业技术水平，制定本办法。',
    publishTime: '2024-01-10',
    source: '卫健委',
    type: 'policy',
    isImportant: false
  },
  {
    id: 3,
    title: '考试系统使用指南',
    content: '本指南详细介绍了疾控医护考试系统的使用方法，包括注册、登录、考试等功能。',
    publishTime: '2024-01-08',
    source: '技术部',
    type: 'news',
    isImportant: false
  }
]

const allList = ref(mockData)

// 计算当前列表
const currentList = computed(() => {
  const currentTabKey = tabList[currentTab.value].key
  if (currentTabKey === 'all') {
    return allList.value
  }
  return allList.value.filter(item => item.type === currentTabKey)
})

// 切换Tab
const onTabChange = (index: number) => {
  currentTab.value = index
}

// 加载更多
const onLoadMore = () => {
  console.log('加载更多')
}

// 查看详情
const viewDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/info/detail?id=${item.id}`
  })
}
</script>

<style lang="scss" scoped>
.info-center-container {
  min-height: 100vh;
  background: #F8F9FA;
}

.header {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  padding: 20rpx 30rpx 40rpx;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
  }
}

.main-content {
  padding-bottom: 120rpx;
}

.banner-section {
  margin: 30rpx;

  .banner-item {
    position: relative;
    height: 200rpx;
    background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
    padding: 40rpx;
    display: flex;
    align-items: center;
    border-radius: 24rpx;

    .banner-content {
      flex: 1;

      .banner-title {
        display: block;
        font-size: 30rpx;
        font-weight: bold;
        color: #fff;
        margin-bottom: 16rpx;
        line-height: 1.4;
      }

      .banner-desc {
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.4;
      }
    }

    .banner-tag {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      padding: 8rpx 16rpx;
      background: rgba(245, 34, 45, 0.9);
      border-radius: 12rpx;
      font-size: 20rpx;
      color: #fff;
    }
  }
}

.content-categories {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .tab-list {
    display: flex;

    .tab-item {
      flex: 1;
      text-align: center;
      padding: 30rpx 0;
      font-size: 28rpx;
      color: #666;
      border-bottom: 4rpx solid transparent;

      &.active {
        color: #2E8B57;
        border-bottom-color: #2E8B57;
        font-weight: bold;
      }
    }
  }
}

.content-list {
  .list-scroll {
    height: calc(100vh - 400rpx);
  }

  .content-item {
    margin: 20rpx 30rpx;
    padding: 40rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);

    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20rpx;
      gap: 16rpx;

      .item-title {
        flex: 1;
        font-size: 30rpx;
        font-weight: bold;
        color: #262626;
        line-height: 1.4;
      }

      .important-tag {
        padding: 8rpx 16rpx;
        background: #fff3e0;
        border-radius: 12rpx;
        border: 2rpx solid #FAAD14;

        text {
          font-size: 20rpx;
          color: #FAAD14;
          font-weight: bold;
        }
      }
    }

    .item-summary {
      display: block;
      font-size: 26rpx;
      color: #595959;
      line-height: 1.5;
      margin-bottom: 24rpx;
    }

    .item-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-date {
        font-size: 24rpx;
        color: #BFBFBF;
      }

      .item-source {
        font-size: 24rpx;
        color: #BFBFBF;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;

  .empty-text {
    font-size: 28rpx;
    color: #BFBFBF;
  }
}
</style>
