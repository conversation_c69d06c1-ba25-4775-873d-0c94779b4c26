<template>
  <view class="study-center-container">
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">学习中心</text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 学习统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.totalQuestions }}</text>
          <text class="stats-label">累计练习</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.accuracy }}%</text>
          <text class="stats-label">正确率</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ studyStats.totalSessions }}</text>
          <text class="stats-label">练习次数</text>
        </view>
      </view>

      <!-- 学习模块 -->
      <view class="study-modules">
        <!-- 教材学习模块（预留） -->
        <view class="module-card disabled" @click="handleTextbookClick">
          <view class="module-icon textbook">
            <text class="icon-text">📚</text>
            <view class="coming-soon-badge">
              <text>即将上线</text>
            </view>
          </view>
          <view class="module-content">
            <text class="module-title">教材学习</text>
            <text class="module-desc">系统性学习专业教材内容</text>
            <text class="module-status">功能建设中，敬请期待</text>
          </view>
        </view>

        <!-- 题库练习模块 -->
        <view class="module-card" @click="handleQuestionBankClick">
          <view class="module-icon question-bank">
            <text class="icon-text">✏️</text>
          </view>
          <view class="module-content">
            <text class="module-title">题库练习</text>
            <text class="module-desc">分类题库专项练习，即时反馈</text>
            <text class="module-info">开始练习</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 模拟学习统计数据
const studyStats = computed(() => ({
  totalQuestions: 156,
  accuracy: 85,
  totalSessions: 12
}))

// 教材点击事件
const handleTextbookClick = () => {
  uni.showToast({
    title: '教材功能正在建设中',
    icon: 'none'
  })
}

// 题库练习点击事件
const handleQuestionBankClick = () => {
  uni.navigateTo({
    url: '/pages/study/category'
  })
}
</script>

<style lang="scss" scoped>
.study-center-container {
  min-height: 100vh;
  background: #F8F9FA;
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
  }
}

.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx;
}

.stats-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;

  .stats-item {
    flex: 1;
    text-align: center;

    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #4A90E2;
      margin-bottom: 8rpx;
    }

    .stats-label {
      font-size: 24rpx;
      color: #666;
    }
  }

  .stats-divider {
    width: 2rpx;
    height: 60rpx;
    background: #f0f0f0;
  }
}

.study-modules {
  margin-bottom: 40rpx;

  .module-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;

    &.disabled {
      opacity: 0.6;

      .module-content {
        .module-status {
          color: #999;
          font-size: 24rpx;
        }
      }
    }

    .module-icon {
      position: relative;
      margin-right: 40rpx;
      width: 120rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 20rpx;

      .icon-text {
        font-size: 60rpx;
      }

      &.textbook {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      }

      &.question-bank {
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
      }

      .coming-soon-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        border-radius: 12rpx;
        padding: 4rpx 12rpx;
        background: #FF9500;

        text {
          font-size: 18rpx;
          color: #fff;
          font-weight: bold;
        }
      }
    }

    .module-content {
      flex: 1;

      .module-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 12rpx;
      }

      .module-desc {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }

      .module-info {
        display: block;
        font-size: 24rpx;
        color: #4A90E2;
      }
      
      .module-status {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.vip-preview {
  margin-bottom: 40rpx;
  
  .vip-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 40rpx;
    color: #fff;
    
    .vip-header {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;
      
      .vip-icon {
        margin-right: 24rpx;
      }
      
      .vip-content {
        flex: 1;
        
        .vip-title {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 12rpx;
        }
        
        .vip-desc {
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }
    
    .vip-features {
      margin-bottom: 40rpx;
      
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        text {
          margin-left: 16rpx;
          font-size: 26rpx;
        }
      }
    }
    
    .vip-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      color: #fff;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }
  }
}

.recent-practices {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .section-more {
      font-size: 26rpx;
      color: #4A90E2;
    }
  }
  
  .practice-list {
    .practice-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .practice-info {
        flex: 1;
        
        .practice-category {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .practice-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .practice-result {
        text-align: right;
        
        .score {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
          
          &.good {
            color: #4CAF50;
          }
          
          &.normal {
            color: #FF9500;
          }
          
          &.poor {
            color: #f56c6c;
          }
        }
        
        .accuracy {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
