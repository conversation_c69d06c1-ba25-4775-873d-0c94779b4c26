{"version": 3, "file": "study.js", "sources": ["pages/study/study.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvc3R1ZHkudnVl"], "sourcesContent": ["<template>\n  <view class=\"study-center-container\">\n    <!-- 自定义头部 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">学习中心</text>\n      </view>\n    </view>\n\n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 学习统计卡片 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.totalQuestions }}</text>\n          <text class=\"stats-label\">累计练习</text>\n        </view>\n        <view class=\"stats-divider\"></view>\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.accuracy }}%</text>\n          <text class=\"stats-label\">正确率</text>\n        </view>\n        <view class=\"stats-divider\"></view>\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.totalSessions }}</text>\n          <text class=\"stats-label\">练习次数</text>\n        </view>\n      </view>\n\n      <!-- 学习模块 -->\n      <view class=\"study-modules\">\n        <!-- 教材学习模块（预留） -->\n        <view class=\"module-card disabled\" @click=\"handleTextbookClick\">\n          <view class=\"module-icon textbook\">\n            <text class=\"icon-text\">📚</text>\n            <view class=\"coming-soon-badge\">\n              <text>即将上线</text>\n            </view>\n          </view>\n          <view class=\"module-content\">\n            <text class=\"module-title\">教材学习</text>\n            <text class=\"module-desc\">系统性学习专业教材内容</text>\n            <text class=\"module-status\">功能建设中，敬请期待</text>\n          </view>\n        </view>\n\n        <!-- 题库练习模块 -->\n        <view class=\"module-card\" @click=\"handleQuestionBankClick\">\n          <view class=\"module-icon question-bank\">\n            <text class=\"icon-text\">✏️</text>\n          </view>\n          <view class=\"module-content\">\n            <text class=\"module-title\">题库练习</text>\n            <text class=\"module-desc\">分类题库专项练习，即时反馈</text>\n            <text class=\"module-info\">开始练习</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed } from 'vue'\n\n// 模拟学习统计数据\nconst studyStats = computed(() => ({\n  totalQuestions: 156,\n  accuracy: 85,\n  totalSessions: 12\n}))\n\n// 教材点击事件\nconst handleTextbookClick = () => {\n  uni.showToast({\n    title: '教材功能正在建设中',\n    icon: 'none'\n  })\n}\n\n// 题库练习点击事件\nconst handleQuestionBankClick = () => {\n  uni.navigateTo({\n    url: '/pages/study/category'\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.study-center-container {\n  min-height: 100vh;\n  background: #F8F9FA;\n}\n\n.header {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n  padding: 20rpx 30rpx 40rpx;\n\n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n\n    .page-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n  }\n}\n\n.main-content {\n  padding: 30rpx;\n  padding-bottom: 120rpx;\n}\n\n.stats-card {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n\n  .stats-item {\n    flex: 1;\n    text-align: center;\n\n    .stats-number {\n      display: block;\n      font-size: 48rpx;\n      font-weight: bold;\n      color: #4A90E2;\n      margin-bottom: 8rpx;\n    }\n\n    .stats-label {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n\n  .stats-divider {\n    width: 2rpx;\n    height: 60rpx;\n    background: #f0f0f0;\n  }\n}\n\n.study-modules {\n  margin-bottom: 40rpx;\n\n  .module-card {\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n    display: flex;\n    align-items: center;\n\n    &.disabled {\n      opacity: 0.6;\n\n      .module-content {\n        .module-status {\n          color: #999;\n          font-size: 24rpx;\n        }\n      }\n    }\n\n    .module-icon {\n      position: relative;\n      margin-right: 40rpx;\n      width: 120rpx;\n      height: 120rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 20rpx;\n\n      .icon-text {\n        font-size: 60rpx;\n      }\n\n      &.textbook {\n        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n      }\n\n      &.question-bank {\n        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);\n      }\n\n      .coming-soon-badge {\n        position: absolute;\n        top: -10rpx;\n        right: -10rpx;\n        border-radius: 12rpx;\n        padding: 4rpx 12rpx;\n        background: #FF9500;\n\n        text {\n          font-size: 18rpx;\n          color: #fff;\n          font-weight: bold;\n        }\n      }\n    }\n\n    .module-content {\n      flex: 1;\n\n      .module-title {\n        display: block;\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 12rpx;\n      }\n\n      .module-desc {\n        display: block;\n        font-size: 26rpx;\n        color: #666;\n        margin-bottom: 8rpx;\n      }\n\n      .module-info {\n        display: block;\n        font-size: 24rpx;\n        color: #4A90E2;\n      }\n      \n      .module-status {\n        display: block;\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n  }\n}\n\n.vip-preview {\n  margin-bottom: 40rpx;\n  \n  .vip-card {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 24rpx;\n    padding: 40rpx;\n    color: #fff;\n    \n    .vip-header {\n      display: flex;\n      align-items: center;\n      margin-bottom: 40rpx;\n      \n      .vip-icon {\n        margin-right: 24rpx;\n      }\n      \n      .vip-content {\n        flex: 1;\n        \n        .vip-title {\n          display: block;\n          font-size: 32rpx;\n          font-weight: bold;\n          margin-bottom: 12rpx;\n        }\n        \n        .vip-desc {\n          font-size: 24rpx;\n          opacity: 0.8;\n        }\n      }\n    }\n    \n    .vip-features {\n      margin-bottom: 40rpx;\n      \n      .feature-item {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        text {\n          margin-left: 16rpx;\n          font-size: 26rpx;\n        }\n      }\n    }\n    \n    .vip-btn {\n      width: 100%;\n      height: 80rpx;\n      border-radius: 40rpx;\n      background: rgba(255, 255, 255, 0.2);\n      color: #fff;\n      border: 2rpx solid rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n.recent-practices {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 24rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n    \n    .section-more {\n      font-size: 26rpx;\n      color: #4A90E2;\n    }\n  }\n  \n  .practice-list {\n    .practice-item {\n      background: #fff;\n      border-radius: 16rpx;\n      padding: 32rpx;\n      margin-bottom: 16rpx;\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .practice-info {\n        flex: 1;\n        \n        .practice-category {\n          display: block;\n          font-size: 28rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 8rpx;\n        }\n        \n        .practice-time {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n      \n      .practice-result {\n        text-align: right;\n        \n        .score {\n          display: block;\n          font-size: 32rpx;\n          font-weight: bold;\n          margin-bottom: 4rpx;\n          \n          &.good {\n            color: #4CAF50;\n          }\n          \n          &.normal {\n            color: #FF9500;\n          }\n          \n          &.poor {\n            color: #f56c6c;\n          }\n        }\n        \n        .accuracy {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/study/study.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "uni"], "mappings": ";;;;;AAkEM,UAAA,aAAaA,cAAAA,SAAS,OAAO;AAAA,MACjC,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,eAAe;AAAA,IACf,EAAA;AAGF,UAAM,sBAAsB,MAAM;AAChCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MAAA,CACP;AAAA,IAAA;AAIH,UAAM,0BAA0B,MAAM;AACpCA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MAAA,CACN;AAAA,IAAA;;;;;;;;;;;;;ACnFH,GAAG,WAAW,eAAe;"}