/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.study-center-container.data-v-3f273c1e {
  min-height: 100vh;
  background: #F8F9FA;
}
.header.data-v-3f273c1e {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
}
.header .header-content.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header .header-content .page-title.data-v-3f273c1e {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}
.main-content.data-v-3f273c1e {
  padding: 30rpx;
  padding-bottom: 120rpx;
}
.stats-card.data-v-3f273c1e {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
}
.stats-card .stats-item.data-v-3f273c1e {
  flex: 1;
  text-align: center;
}
.stats-card .stats-item .stats-number.data-v-3f273c1e {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #4A90E2;
  margin-bottom: 8rpx;
}
.stats-card .stats-item .stats-label.data-v-3f273c1e {
  font-size: 24rpx;
  color: #666;
}
.stats-card .stats-divider.data-v-3f273c1e {
  width: 2rpx;
  height: 60rpx;
  background: #f0f0f0;
}
.study-modules.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.study-modules .module-card.data-v-3f273c1e {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
}
.study-modules .module-card.disabled.data-v-3f273c1e {
  opacity: 0.6;
}
.study-modules .module-card.disabled .module-content .module-status.data-v-3f273c1e {
  color: #999;
  font-size: 24rpx;
}
.study-modules .module-card .module-icon.data-v-3f273c1e {
  position: relative;
  margin-right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20rpx;
}
.study-modules .module-card .module-icon .icon-text.data-v-3f273c1e {
  font-size: 60rpx;
}
.study-modules .module-card .module-icon.textbook.data-v-3f273c1e {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}
.study-modules .module-card .module-icon.question-bank.data-v-3f273c1e {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
}
.study-modules .module-card .module-icon .coming-soon-badge.data-v-3f273c1e {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  border-radius: 12rpx;
  padding: 4rpx 12rpx;
  background: #FF9500;
}
.study-modules .module-card .module-icon .coming-soon-badge text.data-v-3f273c1e {
  font-size: 18rpx;
  color: #fff;
  font-weight: bold;
}
.study-modules .module-card .module-content.data-v-3f273c1e {
  flex: 1;
}
.study-modules .module-card .module-content .module-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.study-modules .module-card .module-content .module-desc.data-v-3f273c1e {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.study-modules .module-card .module-content .module-info.data-v-3f273c1e {
  display: block;
  font-size: 24rpx;
  color: #4A90E2;
}
.study-modules .module-card .module-content .module-status.data-v-3f273c1e {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.vip-preview.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.vip-preview .vip-card.data-v-3f273c1e {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  color: #fff;
}
.vip-preview .vip-card .vip-header.data-v-3f273c1e {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.vip-preview .vip-card .vip-header .vip-icon.data-v-3f273c1e {
  margin-right: 24rpx;
}
.vip-preview .vip-card .vip-header .vip-content.data-v-3f273c1e {
  flex: 1;
}
.vip-preview .vip-card .vip-header .vip-content .vip-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}
.vip-preview .vip-card .vip-header .vip-content .vip-desc.data-v-3f273c1e {
  font-size: 24rpx;
  opacity: 0.8;
}
.vip-preview .vip-card .vip-features.data-v-3f273c1e {
  margin-bottom: 40rpx;
}
.vip-preview .vip-card .vip-features .feature-item.data-v-3f273c1e {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.vip-preview .vip-card .vip-features .feature-item.data-v-3f273c1e:last-child {
  margin-bottom: 0;
}
.vip-preview .vip-card .vip-features .feature-item text.data-v-3f273c1e {
  margin-left: 16rpx;
  font-size: 26rpx;
}
.vip-preview .vip-card .vip-btn.data-v-3f273c1e {
  width: 100%;
  height: 80rpx;
  border-radius: 40rpx;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}
.recent-practices .section-header.data-v-3f273c1e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.recent-practices .section-header .section-title.data-v-3f273c1e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.recent-practices .section-header .section-more.data-v-3f273c1e {
  font-size: 26rpx;
  color: #4A90E2;
}
.recent-practices .practice-list .practice-item.data-v-3f273c1e {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.recent-practices .practice-list .practice-item .practice-info.data-v-3f273c1e {
  flex: 1;
}
.recent-practices .practice-list .practice-item .practice-info .practice-category.data-v-3f273c1e {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.recent-practices .practice-list .practice-item .practice-info .practice-time.data-v-3f273c1e {
  font-size: 24rpx;
  color: #999;
}
.recent-practices .practice-list .practice-item .practice-result.data-v-3f273c1e {
  text-align: right;
}
.recent-practices .practice-list .practice-item .practice-result .score.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.recent-practices .practice-list .practice-item .practice-result .score.good.data-v-3f273c1e {
  color: #4CAF50;
}
.recent-practices .practice-list .practice-item .practice-result .score.normal.data-v-3f273c1e {
  color: #FF9500;
}
.recent-practices .practice-list .practice-item .practice-result .score.poor.data-v-3f273c1e {
  color: #f56c6c;
}
.recent-practices .practice-list .practice-item .practice-result .accuracy.data-v-3f273c1e {
  font-size: 24rpx;
  color: #999;
}