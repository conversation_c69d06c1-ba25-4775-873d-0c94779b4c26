"use strict";
const src_constants_index = require("../constants/index.js");
function formatDate(date, format = "YYYY-MM-DD HH:mm:ss") {
  const d = new Date(date);
  if (isNaN(d.getTime())) {
    return "";
  }
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  const hours = String(d.getHours()).padStart(2, "0");
  const minutes = String(d.getMinutes()).padStart(2, "0");
  const seconds = String(d.getSeconds()).padStart(2, "0");
  return format.replace("YYYY", String(year)).replace("MM", month).replace("DD", day).replace("HH", hours).replace("mm", minutes).replace("ss", seconds);
}
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor(seconds % 3600 / 60);
  const secs = seconds % 60;
  if (hours > 0) {
    return `${hours}:${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
  } else {
    return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
  }
}
function validatePhone(phone) {
  return src_constants_index.REGEX.PHONE.test(phone);
}
function validateIdCard(idCard) {
  return src_constants_index.REGEX.ID_CARD.test(idCard);
}
function maskIdCard(idCard) {
  if (!idCard || idCard.length !== 18)
    return idCard;
  return idCard.replace(/(\d{6})\d{8}(\d{4})/, "$1********$2");
}
exports.formatDate = formatDate;
exports.formatDuration = formatDuration;
exports.maskIdCard = maskIdCard;
exports.validateIdCard = validateIdCard;
exports.validatePhone = validatePhone;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/utils/index.js.map
