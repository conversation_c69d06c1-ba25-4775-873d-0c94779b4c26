"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  setup(__props) {
    const isCollected = common_vendor.ref(false);
    const detail = common_vendor.ref({
      id: 1,
      title: "关于开展2024年疾控医护人员任职资格考试的通知",
      content: `为进一步提高疾控医护人员专业素质，规范任职资格管理，现决定开展2024年疾控医护人员任职资格考试。

一、考试时间
2024年3月15日-3月30日

二、考试对象
全市疾控系统医护人员

三、考试内容
1. 疾病预防控制基础知识
2. 流行病学调查方法
3. 实验室检测技术
4. 应急处置能力

四、报名方式
请登录疾控医护考试系统进行在线报名。

五、注意事项
1. 请认真复习相关知识点
2. 考试期间保持网络畅通
3. 如有问题请及时联系技术支持

特此通知。`,
      publishTime: "2024-01-15",
      source: "疾控中心",
      type: "notice",
      isImportant: true
    });
    common_vendor.onLoad((options) => {
      if (options.id) {
        common_vendor.index.__f__("log", "at pages/info/detail.vue:88", "详情页ID:", options.id);
      }
    });
    const handleShare = () => {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    };
    const handleCollect = () => {
      isCollected.value = !isCollected.value;
      common_vendor.index.showToast({
        title: isCollected.value ? "收藏成功" : "已取消收藏",
        icon: "success"
      });
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: detail.value.isImportant
      }, detail.value.isImportant ? {} : {}, {
        b: common_vendor.t(detail.value.title),
        c: common_vendor.t(detail.value.publishTime),
        d: detail.value.source
      }, detail.value.source ? {
        e: common_vendor.t(detail.value.source)
      } : {}, {
        f: common_vendor.t(detail.value.content),
        g: common_vendor.o(handleShare),
        h: common_vendor.t(isCollected.value ? "已收藏" : "收藏"),
        i: common_vendor.o(handleCollect),
        j: common_vendor.o(goBack)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ab57d952"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/info/detail.js.map
