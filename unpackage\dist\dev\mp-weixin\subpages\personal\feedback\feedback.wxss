/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
view.data-v-3a7560c1, text.data-v-3a7560c1, button.data-v-3a7560c1, input.data-v-3a7560c1, textarea.data-v-3a7560c1, scroll-view.data-v-3a7560c1 {
  box-sizing: border-box;
}
page.data-v-3a7560c1 {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-3a7560c1 {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-3a7560c1 {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-3a7560c1 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-3a7560c1 {
  display: flex;
}
.flex-column.data-v-3a7560c1 {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-3a7560c1 {
  flex: 1;
}
.flex-wrap.data-v-3a7560c1 {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-3a7560c1 {
  color: #262626;
}
.text-secondary.data-v-3a7560c1 {
  color: #595959;
}
.text-disabled.data-v-3a7560c1 {
  color: #BFBFBF;
}
.text-success.data-v-3a7560c1 {
  color: #52C41A;
}
.text-warning.data-v-3a7560c1 {
  color: #FAAD14;
}
.text-error.data-v-3a7560c1 {
  color: #F5222D;
}
.text-primary-color.data-v-3a7560c1 {
  color: #2E8B57;
}
.text-center.data-v-3a7560c1 {
  text-align: center;
}
.text-left.data-v-3a7560c1 {
  text-align: left;
}
.text-right.data-v-3a7560c1 {
  text-align: right;
}
.text-bold.data-v-3a7560c1 {
  font-weight: bold;
}
.text-normal.data-v-3a7560c1 {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-3a7560c1 {
  font-size: 20rpx;
}
.text-sm.data-v-3a7560c1 {
  font-size: 24rpx;
}
.text-base.data-v-3a7560c1 {
  font-size: 28rpx;
}
.text-lg.data-v-3a7560c1 {
  font-size: 32rpx;
}
.text-xl.data-v-3a7560c1 {
  font-size: 36rpx;
}
.text-2xl.data-v-3a7560c1 {
  font-size: 40rpx;
}
.text-3xl.data-v-3a7560c1 {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-3a7560c1 {
  margin: 0;
}
.m-1.data-v-3a7560c1 {
  margin: 8rpx;
}
.m-2.data-v-3a7560c1 {
  margin: 16rpx;
}
.m-3.data-v-3a7560c1 {
  margin: 24rpx;
}
.m-4.data-v-3a7560c1 {
  margin: 32rpx;
}
.m-5.data-v-3a7560c1 {
  margin: 40rpx;
}
.mt-0.data-v-3a7560c1 {
  margin-top: 0;
}
.mt-1.data-v-3a7560c1 {
  margin-top: 8rpx;
}
.mt-2.data-v-3a7560c1 {
  margin-top: 16rpx;
}
.mt-3.data-v-3a7560c1 {
  margin-top: 24rpx;
}
.mt-4.data-v-3a7560c1 {
  margin-top: 32rpx;
}
.mt-5.data-v-3a7560c1 {
  margin-top: 40rpx;
}
.mb-0.data-v-3a7560c1 {
  margin-bottom: 0;
}
.mb-1.data-v-3a7560c1 {
  margin-bottom: 8rpx;
}
.mb-2.data-v-3a7560c1 {
  margin-bottom: 16rpx;
}
.mb-3.data-v-3a7560c1 {
  margin-bottom: 24rpx;
}
.mb-4.data-v-3a7560c1 {
  margin-bottom: 32rpx;
}
.mb-5.data-v-3a7560c1 {
  margin-bottom: 40rpx;
}
.ml-0.data-v-3a7560c1 {
  margin-left: 0;
}
.ml-1.data-v-3a7560c1 {
  margin-left: 8rpx;
}
.ml-2.data-v-3a7560c1 {
  margin-left: 16rpx;
}
.ml-3.data-v-3a7560c1 {
  margin-left: 24rpx;
}
.ml-4.data-v-3a7560c1 {
  margin-left: 32rpx;
}
.ml-5.data-v-3a7560c1 {
  margin-left: 40rpx;
}
.mr-0.data-v-3a7560c1 {
  margin-right: 0;
}
.mr-1.data-v-3a7560c1 {
  margin-right: 8rpx;
}
.mr-2.data-v-3a7560c1 {
  margin-right: 16rpx;
}
.mr-3.data-v-3a7560c1 {
  margin-right: 24rpx;
}
.mr-4.data-v-3a7560c1 {
  margin-right: 32rpx;
}
.mr-5.data-v-3a7560c1 {
  margin-right: 40rpx;
}
.p-0.data-v-3a7560c1 {
  padding: 0;
}
.p-1.data-v-3a7560c1 {
  padding: 8rpx;
}
.p-2.data-v-3a7560c1 {
  padding: 16rpx;
}
.p-3.data-v-3a7560c1 {
  padding: 24rpx;
}
.p-4.data-v-3a7560c1 {
  padding: 32rpx;
}
.p-5.data-v-3a7560c1 {
  padding: 40rpx;
}
.pt-0.data-v-3a7560c1 {
  padding-top: 0;
}
.pt-1.data-v-3a7560c1 {
  padding-top: 8rpx;
}
.pt-2.data-v-3a7560c1 {
  padding-top: 16rpx;
}
.pt-3.data-v-3a7560c1 {
  padding-top: 24rpx;
}
.pt-4.data-v-3a7560c1 {
  padding-top: 32rpx;
}
.pt-5.data-v-3a7560c1 {
  padding-top: 40rpx;
}
.pb-0.data-v-3a7560c1 {
  padding-bottom: 0;
}
.pb-1.data-v-3a7560c1 {
  padding-bottom: 8rpx;
}
.pb-2.data-v-3a7560c1 {
  padding-bottom: 16rpx;
}
.pb-3.data-v-3a7560c1 {
  padding-bottom: 24rpx;
}
.pb-4.data-v-3a7560c1 {
  padding-bottom: 32rpx;
}
.pb-5.data-v-3a7560c1 {
  padding-bottom: 40rpx;
}
.pl-0.data-v-3a7560c1 {
  padding-left: 0;
}
.pl-1.data-v-3a7560c1 {
  padding-left: 8rpx;
}
.pl-2.data-v-3a7560c1 {
  padding-left: 16rpx;
}
.pl-3.data-v-3a7560c1 {
  padding-left: 24rpx;
}
.pl-4.data-v-3a7560c1 {
  padding-left: 32rpx;
}
.pl-5.data-v-3a7560c1 {
  padding-left: 40rpx;
}
.pr-0.data-v-3a7560c1 {
  padding-right: 0;
}
.pr-1.data-v-3a7560c1 {
  padding-right: 8rpx;
}
.pr-2.data-v-3a7560c1 {
  padding-right: 16rpx;
}
.pr-3.data-v-3a7560c1 {
  padding-right: 24rpx;
}
.pr-4.data-v-3a7560c1 {
  padding-right: 32rpx;
}
.pr-5.data-v-3a7560c1 {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-3a7560c1 {
  width: 100%;
}
.h-full.data-v-3a7560c1 {
  height: 100%;
}
.w-screen.data-v-3a7560c1 {
  width: 100vw;
}
.h-screen.data-v-3a7560c1 {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-3a7560c1 {
  border-radius: 0;
}
.rounded-sm.data-v-3a7560c1 {
  border-radius: 4rpx;
}
.rounded.data-v-3a7560c1 {
  border-radius: 8rpx;
}
.rounded-lg.data-v-3a7560c1 {
  border-radius: 16rpx;
}
.rounded-xl.data-v-3a7560c1 {
  border-radius: 24rpx;
}
.rounded-full.data-v-3a7560c1 {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-3a7560c1 {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-3a7560c1 {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-3a7560c1 {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-3a7560c1 {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-3a7560c1 {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-3a7560c1 {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-3a7560c1 {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-3a7560c1 {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-3a7560c1 {
  background-color: #2E8B57;
}
.bg-light.data-v-3a7560c1 {
  background-color: #FFFFFF;
}
.bg-gray.data-v-3a7560c1 {
  background-color: #F8F9FA;
}
.bg-success.data-v-3a7560c1 {
  background-color: #52C41A;
}
.bg-warning.data-v-3a7560c1 {
  background-color: #FAAD14;
}
.bg-error.data-v-3a7560c1 {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-3a7560c1 {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-3a7560c1 {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-3a7560c1 {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-3a7560c1 {
  color: #FAAD14;
}
.status-approved.data-v-3a7560c1 {
  color: #52C41A;
}
.status-rejected.data-v-3a7560c1 {
  color: #F5222D;
}
.status-not-submitted.data-v-3a7560c1 {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-3a7560c1 {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-3a7560c1 {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-3a7560c1 {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-3a7560c1 {
  animation: fadeIn-3a7560c1 0.3s ease-in-out;
}
@keyframes fadeIn-3a7560c1 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-3a7560c1 {
  animation: slideUp-3a7560c1 0.3s ease-out;
}
@keyframes slideUp-3a7560c1 {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-3a7560c1::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-3a7560c1 {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-3a7560c1,
.fade-leave-active.data-v-3a7560c1 {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-3a7560c1,
.fade-leave-to.data-v-3a7560c1 {
  opacity: 0;
}
.slide-up-enter-active.data-v-3a7560c1,
.slide-up-leave-active.data-v-3a7560c1 {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-3a7560c1,
.slide-up-leave-to.data-v-3a7560c1 {
  transform: translateY(100%);
}
.feedback-container.data-v-3a7560c1 {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 120rpx;
}
.feedback-form.data-v-3a7560c1 {
  padding: 24rpx;
}
.form-section.data-v-3a7560c1 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.form-section .section-title.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.form-section .section-title text.data-v-3a7560c1 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.form-section .section-title .required.data-v-3a7560c1 {
  color: #f56c6c;
  margin-left: 8rpx;
}
.form-section .section-title .optional.data-v-3a7560c1 {
  color: #999;
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 8rpx;
}
.feedback-types.data-v-3a7560c1 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.feedback-types .type-item.data-v-3a7560c1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.feedback-types .type-item.active.data-v-3a7560c1 {
  background: #2E8B57;
  border-color: #2E8B57;
}
.feedback-types .type-item.active text.data-v-3a7560c1 {
  color: #fff;
}
.feedback-types .type-item text.data-v-3a7560c1 {
  margin-top: 12rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}
.textarea-container.data-v-3a7560c1 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}
.contact-inputs.data-v-3a7560c1 {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}
.image-upload .uploaded-images.data-v-3a7560c1 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.image-upload .uploaded-images .image-item.data-v-3a7560c1 {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}
.image-upload .uploaded-images .image-item .uploaded-image.data-v-3a7560c1 {
  width: 100%;
  height: 100%;
}
.image-upload .uploaded-images .image-item .image-delete.data-v-3a7560c1 {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-upload .uploaded-images .upload-btn.data-v-3a7560c1 {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #2E8B57;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.image-upload .uploaded-images .upload-btn .upload-text.data-v-3a7560c1 {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #2E8B57;
}
.image-upload .uploaded-images .upload-btn .upload-limit.data-v-3a7560c1 {
  margin-top: 8rpx;
  font-size: 20rpx;
  color: #999;
}
.image-upload .upload-tips text.data-v-3a7560c1 {
  font-size: 24rpx;
  color: #999;
}
.faq-list .faq-item.data-v-3a7560c1 {
  border-bottom: 2rpx solid #f8f9fa;
}
.faq-list .faq-item.data-v-3a7560c1:last-child {
  border-bottom: none;
}
.faq-list .faq-item .faq-question.data-v-3a7560c1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
}
.faq-list .faq-item .faq-question text.data-v-3a7560c1 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.faq-list .faq-item .faq-answer.data-v-3a7560c1 {
  padding-bottom: 24rpx;
}
.faq-list .faq-item .faq-answer text.data-v-3a7560c1 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
.bottom-actions.data-v-3a7560c1 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.bottom-actions .submit-btn.data-v-3a7560c1 {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}