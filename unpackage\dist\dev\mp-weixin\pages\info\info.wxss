/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.info-center-container.data-v-f52d2d81 {
  min-height: 100vh;
  background: #F8F9FA;
}
.header.data-v-f52d2d81 {
  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);
  padding: 20rpx 30rpx 40rpx;
}
.header .header-content.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header .header-content .page-title.data-v-f52d2d81 {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}
.main-content.data-v-f52d2d81 {
  padding-bottom: 120rpx;
}
.banner-section.data-v-f52d2d81 {
  margin: 30rpx;
}
.banner-section .banner-item.data-v-f52d2d81 {
  position: relative;
  height: 200rpx;
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 100%);
  padding: 40rpx;
  display: flex;
  align-items: center;
  border-radius: 24rpx;
}
.banner-section .banner-item .banner-content.data-v-f52d2d81 {
  flex: 1;
}
.banner-section .banner-item .banner-content .banner-title.data-v-f52d2d81 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  line-height: 1.4;
}
.banner-section .banner-item .banner-content .banner-desc.data-v-f52d2d81 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}
.banner-section .banner-item .banner-tag.data-v-f52d2d81 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  background: rgba(245, 34, 45, 0.9);
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}
.content-categories.data-v-f52d2d81 {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.content-categories .tab-list.data-v-f52d2d81 {
  display: flex;
}
.content-categories .tab-list .tab-item.data-v-f52d2d81 {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}
.content-categories .tab-list .tab-item.active.data-v-f52d2d81 {
  color: #2E8B57;
  border-bottom-color: #2E8B57;
  font-weight: bold;
}
.content-list .list-scroll.data-v-f52d2d81 {
  height: calc(100vh - 400rpx);
}
.content-list .content-item.data-v-f52d2d81 {
  margin: 20rpx 30rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.content-list .content-item .item-header.data-v-f52d2d81 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
  gap: 16rpx;
}
.content-list .content-item .item-header .item-title.data-v-f52d2d81 {
  flex: 1;
  font-size: 30rpx;
  font-weight: bold;
  color: #262626;
  line-height: 1.4;
}
.content-list .content-item .item-header .important-tag.data-v-f52d2d81 {
  padding: 8rpx 16rpx;
  background: #fff3e0;
  border-radius: 12rpx;
  border: 2rpx solid #FAAD14;
}
.content-list .content-item .item-header .important-tag text.data-v-f52d2d81 {
  font-size: 20rpx;
  color: #FAAD14;
  font-weight: bold;
}
.content-list .content-item .item-summary.data-v-f52d2d81 {
  display: block;
  font-size: 26rpx;
  color: #595959;
  line-height: 1.5;
  margin-bottom: 24rpx;
}
.content-list .content-item .item-footer.data-v-f52d2d81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.content-list .content-item .item-footer .item-date.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #BFBFBF;
}
.content-list .content-item .item-footer .item-source.data-v-f52d2d81 {
  font-size: 24rpx;
  color: #BFBFBF;
}
.empty-state.data-v-f52d2d81 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state .empty-text.data-v-f52d2d81 {
  font-size: 28rpx;
  color: #BFBFBF;
}