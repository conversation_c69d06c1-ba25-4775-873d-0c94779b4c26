"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const agreedToTerms = common_vendor.ref(false);
    const isLogging = common_vendor.ref(false);
    const onAgreementChange = (e) => {
      agreedToTerms.value = e.detail.value.length > 0;
    };
    const handleLogin = () => __async(this, null, function* () {
      if (!agreedToTerms.value) {
        common_vendor.index.showToast({
          title: "请先同意用户协议",
          icon: "none"
        });
        return;
      }
      isLogging.value = true;
      try {
        yield new Promise((resolve) => setTimeout(resolve, 1e3));
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/info/info"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:92", "登录失败:", error);
        common_vendor.index.showToast({
          title: "登录失败，请重试",
          icon: "none"
        });
      } finally {
        isLogging.value = false;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: agreedToTerms.value,
        b: common_vendor.o(onAgreementChange),
        c: common_vendor.t(isLogging.value ? "登录中..." : "开始使用"),
        d: !agreedToTerms.value || isLogging.value,
        e: common_vendor.o(handleLogin)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
