"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "study",
  setup(__props) {
    const studyStats = common_vendor.computed(() => ({
      totalQuestions: 156,
      accuracy: 85,
      totalSessions: 12
    }));
    const handleTextbookClick = () => {
      common_vendor.index.showToast({
        title: "教材功能正在建设中",
        icon: "none"
      });
    };
    const handleQuestionBankClick = () => {
      common_vendor.index.navigateTo({
        url: "/pages/study/category"
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(studyStats.value.totalQuestions),
        b: common_vendor.t(studyStats.value.accuracy),
        c: common_vendor.t(studyStats.value.totalSessions),
        d: common_vendor.o(handleTextbookClick),
        e: common_vendor.o(handleQuestionBankClick)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f273c1e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/study.js.map
