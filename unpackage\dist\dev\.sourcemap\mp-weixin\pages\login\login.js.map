{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- Logo和标题 -->\n      <view class=\"header\">\n        <view class=\"logo-placeholder\">\n          <text class=\"logo-text\">疾控</text>\n        </view>\n        <text class=\"app-name\">疾控医护考试系统</text>\n        <text class=\"app-desc\">医护人员任职资格考试平台</text>\n      </view>\n\n      <!-- 登录表单 -->\n      <view class=\"login-form\">\n        <!-- 协议勾选 -->\n        <view class=\"agreement-section\">\n          <checkbox\n            :checked=\"agreedToTerms\"\n            @change=\"onAgreementChange\"\n            color=\"#2E8B57\"\n          />\n          <text class=\"agreement-text\">\n            我已阅读并同意《用户服务协议》和《隐私政策》\n          </text>\n        </view>\n\n        <!-- 登录按钮 -->\n        <button\n          class=\"login-btn\"\n          :disabled=\"!agreedToTerms || isLogging\"\n          @click=\"handleLogin\"\n        >\n          {{ isLogging ? '登录中...' : '开始使用' }}\n        </button>\n\n        <!-- 温馨提示 -->\n        <view class=\"tips\">\n          <text class=\"tips-text\">首次登录需完善个人资料，通过机构审核后可使用完整功能</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部版权信息 -->\n    <view class=\"footer\">\n      <text class=\"copyright\">© 2024 疾控医护考试系统</text>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref } from 'vue'\n\n// 响应式数据\nconst agreedToTerms = ref(false)\nconst isLogging = ref(false)\n\n// 协议勾选处理\nconst onAgreementChange = (e: any) => {\n  agreedToTerms.value = e.detail.value.length > 0\n}\n\n// 登录处理\nconst handleLogin = async () => {\n  if (!agreedToTerms.value) {\n    uni.showToast({\n      title: '请先同意用户协议',\n      icon: 'none'\n    })\n    return\n  }\n\n  isLogging.value = true\n\n  try {\n    // 模拟登录过程\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    uni.showToast({\n      title: '登录成功',\n      icon: 'success'\n    })\n\n    // 跳转到信息中心页面\n    setTimeout(() => {\n      uni.switchTab({\n        url: '/pages/info/info'\n      })\n    }, 1500)\n\n  } catch (error: any) {\n    uni.__f__('error','at pages/login/login.vue:92','登录失败:', error)\n    uni.showToast({\n      title: '登录失败，请重试',\n      icon: 'none'\n    })\n  } finally {\n    isLogging.value = false\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 40rpx 60rpx;\n}\n\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  max-width: 500rpx;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 100rpx;\n\n  .logo-placeholder {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 24rpx;\n    margin: 0 auto 40rpx;\n    background: rgba(255, 255, 255, 0.2);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\n    .logo-text {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n  }\n\n  .app-name {\n    display: block;\n    font-size: 48rpx;\n    font-weight: bold;\n    color: #fff;\n    margin-bottom: 16rpx;\n    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  }\n\n  .app-desc {\n    display: block;\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n    line-height: 1.4;\n  }\n}\n\n.login-form {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 32rpx;\n  padding: 60rpx 40rpx;\n  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);\n}\n\n.agreement-section {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 40rpx;\n\n  checkbox {\n    margin-right: 12rpx;\n    margin-top: 4rpx;\n  }\n\n  .agreement-text {\n    flex: 1;\n    font-size: 26rpx;\n    color: #666;\n    line-height: 1.5;\n  }\n}\n\n.login-btn {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 44rpx;\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  color: #fff;\n  font-size: 32rpx;\n  font-weight: bold;\n  border: none;\n  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);\n  margin-bottom: 40rpx;\n\n  &:disabled {\n    background: #ccc;\n    box-shadow: none;\n  }\n}\n\n.tips {\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  border-left: 6rpx solid #2E8B57;\n\n  .tips-text {\n    font-size: 24rpx;\n    color: #666;\n    line-height: 1.5;\n  }\n}\n\n.footer {\n  margin-top: 60rpx;\n\n  .copyright {\n    font-size: 24rpx;\n    color: rgba(255, 255, 255, 0.6);\n    text-align: center;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAsDM,UAAA,gBAAgBA,kBAAI,KAAK;AACzB,UAAA,YAAYA,kBAAI,KAAK;AAGrB,UAAA,oBAAoB,CAAC,MAAW;AACpC,oBAAc,QAAQ,EAAE,OAAO,MAAM,SAAS;AAAA,IAAA;AAIhD,UAAM,cAAc,MAAY;AAC1B,UAAA,CAAC,cAAc,OAAO;AACxBC,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEA,gBAAU,QAAQ;AAEd,UAAA;AAEF,cAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,SAAS,GAAI,CAAC;AAEtDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UAAA,CACN;AAAA,WACA,IAAI;AAAA,eAEA,OAAY;AACnBA,sBAAA,MAAI,MAAM,SAAQ,+BAA8B,SAAS,KAAK;AAC9DA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,UACD;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;;;;;;;;;;;;;ACjGF,GAAG,WAAW,eAAe;"}