/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.detail-container.data-v-ab57d952 {
  min-height: 100vh;
  background: #F8F9FA;
  padding: 24rpx;
}
.detail-content.data-v-ab57d952 {
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.article-header.data-v-ab57d952 {
  padding: 40rpx;
}
.article-header .header-tags.data-v-ab57d952 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.article-header .header-tags .important-tag.data-v-ab57d952 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: #fff7e6;
  color: #FAAD14;
}
.article-header .header-tags .important-tag text.data-v-ab57d952 {
  font-weight: bold;
}
.article-header .article-title.data-v-ab57d952 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #262626;
  line-height: 1.4;
  margin-bottom: 32rpx;
}
.article-header .article-meta.data-v-ab57d952 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.article-header .article-meta .meta-item text.data-v-ab57d952 {
  font-size: 26rpx;
  color: #595959;
}
.divider.data-v-ab57d952 {
  height: 2rpx;
  background: #F0F0F0;
  margin: 0 40rpx;
}
.article-body.data-v-ab57d952 {
  padding: 40rpx;
}
.article-body .content-text.data-v-ab57d952 {
  font-size: 30rpx;
  line-height: 1.8;
  color: #262626;
  white-space: pre-line;
}
.article-actions.data-v-ab57d952 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 32rpx 40rpx;
  border-top: 2rpx solid #F0F0F0;
}
.article-actions .action-btn.data-v-ab57d952 {
  padding: 16rpx 32rpx;
  background: #2E8B57;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}